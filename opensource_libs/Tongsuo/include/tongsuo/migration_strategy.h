/*
 * Copyright (C) 2024 The Tongsuo Project.
 *
 * Tongsuo Migration Strategy Header
 * 
 * This file provides a migration path from BoringSSL to Tongsuo while
 * preserving existing BoringSSL usage and enabling gradual transition.
 */

#ifndef TONGSUO_MIGRATION_STRATEGY_H
#define TONGSUO_MIGRATION_STRATEGY_H

/*
 * Migration Strategy Configuration
 * 
 * This allows developers to:
 * 1. Keep existing BoringSSL code unchanged
 * 2. Use Tongsuo for new development 
 * 3. Gradually migrate when ready
 */

/* 
 * Migration Modes:
 * - PRESERVE_BORINGSSL: Keep all existing BoringSSL usage intact
 * - GRADUAL_MIGRATION: Allow selective migration on per-module basis  
 * - TONGSUO_PREFERRED: Use Tongsuo by default for new code
 */

#ifndef TONGSUO_MIGRATION_MODE
#define TONGSUO_MIGRATION_MODE PRESERVE_BORINGSSL
#endif

#define PRESERVE_BORINGSSL    1
#define GRADUAL_MIGRATION     2  
#define TONGSUO_PREFERRED     3

/*
 * Header Selection Strategy
 */

#if TONGSUO_MIGRATION_MODE == PRESERVE_BORINGSSL

/* Mode 1: Preserve BoringSSL - existing code unchanged */
/* Default includes go to BoringSSL, Tongsuo requires explicit namespace */

#define TONGSUO_EXPLICIT_ONLY 1

/* BoringSSL headers work as before */
/* #include <openssl/evp.h>  -> BoringSSL */

/* Tongsuo requires explicit namespace */
/* #include <tongsuo/evp.h>  -> Tongsuo */

#elif TONGSUO_MIGRATION_MODE == GRADUAL_MIGRATION

/* Mode 2: Gradual Migration - per-module control */

#ifndef MODULE_CRYPTO_PROVIDER
#define MODULE_CRYPTO_PROVIDER BORINGSSL  /* Default to BoringSSL */
#endif

#define BORINGSSL     1
#define TONGSUO       2

#if MODULE_CRYPTO_PROVIDER == TONGSUO
/* This module uses Tongsuo */
#define CRYPTO_PROVIDER_TONGSUO 1
#include "tongsuo/openssl_wrapper.h"
#else
/* This module uses BoringSSL */
#define CRYPTO_PROVIDER_BORINGSSL 1
/* Standard OpenSSL headers -> BoringSSL */
#endif

#elif TONGSUO_MIGRATION_MODE == TONGSUO_PREFERRED

/* Mode 3: Tongsuo Preferred - new default */
/* Tongsuo becomes the default, BoringSSL requires explicit namespace */

#define TONGSUO_DEFAULT 1

/* Default includes go to Tongsuo */
/* #include <openssl/evp.h>  -> Tongsuo (via wrapper) */

/* BoringSSL requires explicit namespace */  
/* #include <boringssl/evp.h> -> BoringSSL */

#endif

/*
 * Migration Helper Macros
 */

/* Conditional compilation based on provider */
#ifdef CRYPTO_PROVIDER_TONGSUO
#define IF_TONGSUO(x) x
#define IF_BORINGSSL(x)
#else
#define IF_TONGSUO(x) 
#define IF_BORINGSSL(x) x
#endif

/* Module-specific provider selection */
#define USE_TONGSUO_FOR_MODULE() \
    do { \
        static_assert(MODULE_CRYPTO_PROVIDER == TONGSUO, \
                     "Module not configured for Tongsuo"); \
    } while(0)

#define USE_BORINGSSL_FOR_MODULE() \
    do { \
        static_assert(MODULE_CRYPTO_PROVIDER == BORINGSSL, \
                     "Module not configured for BoringSSL"); \
    } while(0)

/*
 * Migration Utilities
 */

/* Check what crypto provider is active */
static inline const char* get_crypto_provider_name(void) {
#ifdef CRYPTO_PROVIDER_TONGSUO
    return "Tongsuo";
#else
    return "BoringSSL";
#endif
}

/* Feature availability checks */
static inline int crypto_provider_supports_sm2(void) {
#ifdef CRYPTO_PROVIDER_TONGSUO
    return 1;  /* Tongsuo supports SM2 */
#else
    return 0;  /* BoringSSL doesn't support SM2 */
#endif
}

static inline int crypto_provider_supports_ntls(void) {
#ifdef CRYPTO_PROVIDER_TONGSUO  
    return 1;  /* Tongsuo supports NTLS */
#else
    return 0;  /* BoringSSL doesn't support NTLS */
#endif
}

/*
 * Migration Documentation Helpers
 */

#define MIGRATION_NOTICE(old_api, new_api) \
    _Pragma("message \"Consider migrating from " #old_api " to " #new_api "\"")

#define TONGSUO_ONLY_FEATURE(feature) \
    _Pragma("message \"" #feature " is only available with Tongsuo provider\"")

/*
 * Algorithm Routing Macros for Gradual Migration
 */

/* Use these macros to route algorithms based on provider capability */
#define PREFER_TONGSUO_FOR_SM_ALGORITHMS() \
    _Pragma("message \"SM algorithms should use Tongsuo provider\"")

#define ROUTE_ALGORITHM(alg_name) \
    IF_TONGSUO(use_tongsuo_##alg_name()) \
    IF_BORINGSSL(use_boringssl_##alg_name())

/*
 * Migration Validation
 */

/* Compile-time checks for migration consistency */
#if defined(CRYPTO_PROVIDER_TONGSUO) && defined(CRYPTO_PROVIDER_BORINGSSL)
#error "Cannot use both Tongsuo and BoringSSL providers in same module"
#endif

#if TONGSUO_MIGRATION_MODE == GRADUAL_MIGRATION && !defined(MODULE_CRYPTO_PROVIDER)
#warning "MODULE_CRYPTO_PROVIDER not defined in gradual migration mode"
#endif

/*
 * Module Migration Status Tracking
 */
struct migration_status {
    const char* module_name;
    int uses_tongsuo;
    int uses_boringssl; 
    int migration_complete;
    const char* migration_notes;
};

#define DECLARE_MODULE_MIGRATION_STATUS(name, tongsuo, boringssl, complete, notes) \
    static const struct migration_status name##_migration_status = { \
        .module_name = #name, \
        .uses_tongsuo = tongsuo, \
        .uses_boringssl = boringssl, \
        .migration_complete = complete, \
        .migration_notes = notes \
    }

/*
 * Example Migration Declarations for Reference
 */

/* Example: Module still using BoringSSL */
DECLARE_MODULE_MIGRATION_STATUS(legacy_module, 0, 1, 0, 
    "Legacy module, migration planned for Q2 2024");

/* Example: Module migrated to Tongsuo */  
DECLARE_MODULE_MIGRATION_STATUS(new_sm_module, 1, 0, 1,
    "New module using SM algorithms, fully on Tongsuo");

/* Example: Module in transition */
DECLARE_MODULE_MIGRATION_STATUS(hybrid_module, 1, 1, 0,
    "Using Tongsuo for SM algs, BoringSSL for others");

#endif /* TONGSUO_MIGRATION_STRATEGY_H */ 