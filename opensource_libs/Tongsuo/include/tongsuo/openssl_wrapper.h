/*
 * Copyright (C) 2024 The Tongsuo Project.
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
 * OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
 * CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 */

#ifndef TONGSUO_OPENSSL_WRAPPER_H
#define TONGSUO_OPENSSL_WRAPPER_H

/*
 * Tongsuo OpenSSL Compatibility Wrapper
 * 
 * This file provides a namespace-isolated wrapper for Tongsuo when
 * coexisting with BoringSSL in the same binary. It maps standard
 * OpenSSL API calls to Tongsuo's namespaced versions.
 */

#ifdef TONGSUO_COEXIST_WITH_BORINGSSL

/* Prevent inclusion of system OpenSSL headers */
#define OPENSSL_NO_DEPRECATED
#define TONGSUO_INTERNAL_BUILD

/* Include Tongsuo's internal headers with namespace prefix */
#include "tongsuo/evp.h"
#include "tongsuo/opensslv.h"
#include "tongsuo/rand.h"
#include "tongsuo/sm2.h"
#include "tongsuo/sm3.h"
#include "tongsuo/sm4.h"
#include "tongsuo/err.h"

/* 
 * API Mapping Macros
 * Map standard OpenSSL calls to Tongsuo's prefixed versions
 */

/* EVP Functions */
#define EVP_MD_CTX_new              TONGSUO_EVP_MD_CTX_new
#define EVP_MD_CTX_free             TONGSUO_EVP_MD_CTX_free
#define EVP_get_digestbyname        TONGSUO_EVP_get_digestbyname
#define EVP_DigestInit_ex           TONGSUO_EVP_DigestInit_ex
#define EVP_DigestUpdate            TONGSUO_EVP_DigestUpdate
#define EVP_DigestFinal_ex          TONGSUO_EVP_DigestFinal_ex
#define EVP_MAX_MD_SIZE             TONGSUO_EVP_MAX_MD_SIZE

/* Version Functions */
#define OpenSSL_version             TONGSUO_OpenSSL_version
#define OPENSSL_VERSION             TONGSUO_OPENSSL_VERSION

/* Random Functions */
#define RAND_bytes                  TONGSUO_RAND_bytes
#define RAND_priv_bytes             TONGSUO_RAND_priv_bytes

/* SM2 Functions */
#define SM2_sign                    TONGSUO_SM2_sign
#define SM2_verify                  TONGSUO_SM2_verify
#define SM2_encrypt                 TONGSUO_SM2_encrypt
#define SM2_decrypt                 TONGSUO_SM2_decrypt

/* SM3 Functions */
#define SM3_Init                    TONGSUO_SM3_Init
#define SM3_Update                  TONGSUO_SM3_Update
#define SM3_Final                   TONGSUO_SM3_Final

/* SM4 Functions */
#define SM4_set_key                 TONGSUO_SM4_set_key
#define SM4_encrypt                 TONGSUO_SM4_encrypt
#define SM4_decrypt                 TONGSUO_SM4_decrypt

/* Error Functions */
#define ERR_get_error              TONGSUO_ERR_get_error
#define ERR_error_string           TONGSUO_ERR_error_string
#define ERR_clear_error            TONGSUO_ERR_clear_error

/*
 * Type Definitions with Namespace
 */
typedef TONGSUO_EVP_MD_CTX EVP_MD_CTX;
typedef TONGSUO_EVP_MD EVP_MD;

#else /* !TONGSUO_COEXIST_WITH_BORINGSSL */

/* Standard OpenSSL headers when not coexisting */
#include <openssl/evp.h>
#include <openssl/opensslv.h>
#include <openssl/rand.h>
#include <openssl/err.h>

#endif /* TONGSUO_COEXIST_WITH_BORINGSSL */

/*
 * Tongsuo-specific APIs (always available)
 */

/* SM algorithm family - these are Tongsuo extensions */
#ifndef TONGSUO_SM_API_H
#define TONGSUO_SM_API_H

#ifdef __cplusplus
extern "C" {
#endif

/* SM2 Public Key Cryptography */
int tongsuo_sm2_keygen(unsigned char *pubkey, unsigned char *privkey);
int tongsuo_sm2_sign(const unsigned char *msg, size_t msglen,
                     unsigned char *sig, size_t *siglen,
                     const unsigned char *privkey);
int tongsuo_sm2_verify(const unsigned char *msg, size_t msglen,
                       const unsigned char *sig, size_t siglen,
                       const unsigned char *pubkey);

/* SM3 Hash Function */
int tongsuo_sm3_hash(const unsigned char *input, size_t inlen,
                     unsigned char *output);

/* SM4 Block Cipher */
int tongsuo_sm4_encrypt(const unsigned char *input, unsigned char *output,
                        const unsigned char *key);
int tongsuo_sm4_decrypt(const unsigned char *input, unsigned char *output,
                        const unsigned char *key);

#ifdef __cplusplus
}
#endif

#endif /* TONGSUO_SM_API_H */

/* Version and feature detection */
#define TONGSUO_VERSION_MAJOR       8
#define TONGSUO_VERSION_MINOR       4
#define TONGSUO_VERSION_PATCH       0
#define TONGSUO_VERSION_STRING      "8.4.0"

/* Feature flags */
#define TONGSUO_FEATURE_SM2         1
#define TONGSUO_FEATURE_SM3         1
#define TONGSUO_FEATURE_SM4         1
#define TONGSUO_FEATURE_NTLS        1
#define TONGSUO_FEATURE_ZKP         1

#endif /* TONGSUO_OPENSSL_WRAPPER_H */ 