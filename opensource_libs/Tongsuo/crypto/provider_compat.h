/*
 * Copyright (C) 2024 The Tongsuo Project.
 *
 * Provider Compatibility Layer for TEE Environment
 * 
 * This file provides compatibility shims for Tongsuo's provider system
 * in the restricted TEE environment where full provider loading is not available.
 */

#ifndef PROVIDER_COMPAT_H
#define PROVIDER_COMPAT_H

#ifdef __TEE_ENVIRONMENT__

/* Disable dynamic provider loading in TEE */
#define OPENSSL_NO_DYNAMIC_ENGINE 1
#define OPENSSL_NO_ENGINE 1

/* Provider-related function stubs */
#define OSSL_PROVIDER void
#define OSSL_PROVIDER_CTX void

/* Stub provider functions for TEE environment */
static inline void* OSSL_PROVIDER_load(void* libctx, const char* name) {
    (void)libctx;
    (void)name;
    return NULL; /* No dynamic loading in TEE */
}

static inline int OSSL_PROVIDER_unload(void* prov) {
    (void)prov;
    return 1; /* Always success for stub */
}

static inline const char* OSSL_PROVIDER_get0_name(const void* prov) {
    (void)prov;
    return "tee_provider"; /* Static name for TEE */
}

static inline void* OSSL_PROVIDER_get0_provider_ctx(void* prov) {
    (void)prov;
    return NULL;
}

/* Algorithm lookup stubs */
static inline void* EVP_MD_fetch(void* libctx, const char* algorithm, const char* properties) {
    (void)libctx;
    (void)properties;
    
    /* Simple algorithm mapping for TEE environment */
    if (strcmp(algorithm, "SM3") == 0) {
        extern const void* EVP_sm3(void);
        return (void*)EVP_sm3();
    }
    if (strcmp(algorithm, "SHA256") == 0) {
        extern const void* EVP_sha256(void);
        return (void*)EVP_sha256();
    }
    if (strcmp(algorithm, "SHA1") == 0) {
        extern const void* EVP_sha1(void);
        return (void*)EVP_sha1();
    }
    
    return NULL;
}

static inline void EVP_MD_free(void* md) {
    /* No-op for static algorithms */
    (void)md;
}

static inline void* EVP_CIPHER_fetch(void* libctx, const char* algorithm, const char* properties) {
    (void)libctx;
    (void)properties;
    
    /* Simple cipher mapping for TEE environment */
    if (strcmp(algorithm, "SM4") == 0 || strcmp(algorithm, "SM4-ECB") == 0) {
        extern const void* EVP_sm4_ecb(void);
        return (void*)EVP_sm4_ecb();
    }
    if (strcmp(algorithm, "AES-256-ECB") == 0) {
        extern const void* EVP_aes_256_ecb(void);
        return (void*)EVP_aes_256_ecb();
    }
    if (strcmp(algorithm, "AES-128-ECB") == 0) {
        extern const void* EVP_aes_128_ecb(void);
        return (void*)EVP_aes_128_ecb();
    }
    
    return NULL;
}

static inline void EVP_CIPHER_free(void* cipher) {
    /* No-op for static algorithms */
    (void)cipher;
}

/* Key management stubs */
static inline void* EVP_KEYMGMT_fetch(void* libctx, const char* algorithm, const char* properties) {
    (void)libctx;
    (void)algorithm;
    (void)properties;
    return NULL; /* Use legacy key management in TEE */
}

static inline void EVP_KEYMGMT_free(void* keymgmt) {
    (void)keymgmt;
}

/* Signature algorithm stubs */  
static inline void* EVP_SIGNATURE_fetch(void* libctx, const char* algorithm, const char* properties) {
    (void)libctx;
    (void)algorithm;  
    (void)properties;
    return NULL; /* Use legacy signature methods in TEE */
}

static inline void EVP_SIGNATURE_free(void* signature) {
    (void)signature;
}

/* Library context stubs */
static inline void* OSSL_LIB_CTX_new(void) {
    return NULL; /* Use default context in TEE */
}

static inline void OSSL_LIB_CTX_free(void* libctx) {
    (void)libctx;
}

static inline void* OSSL_LIB_CTX_get0_global_default(void) {
    return NULL; /* Default context */
}

/* Property query stubs */
static inline void* OSSL_PROPERTY_LIST_new(void* libctx) {
    (void)libctx;
    return NULL;
}

static inline void OSSL_PROPERTY_LIST_free(void* list) {
    (void)list;
}

/* Store and loader stubs */
#define OSSL_STORE_LOADER void
#define OSSL_STORE_LOADER_CTX void

static inline void* OSSL_STORE_LOADER_fetch(void* libctx, const char* scheme, const char* properties) {
    (void)libctx;
    (void)scheme;
    (void)properties;
    return NULL; /* No store loading in TEE */
}

static inline void OSSL_STORE_LOADER_free(void* loader) {
    (void)loader;
}

/* Parameter building stubs */
#define OSSL_PARAM_BLD void

static inline void* OSSL_PARAM_BLD_new(void) {
    return NULL;
}

static inline void OSSL_PARAM_BLD_free(void* bld) {
    (void)bld;
}

/* Encoder/Decoder stubs */
#define OSSL_ENCODER void
#define OSSL_DECODER void

static inline void* OSSL_ENCODER_fetch(void* libctx, const char* name, const char* properties) {
    (void)libctx;
    (void)name;
    (void)properties;
    return NULL;
}

static inline void OSSL_ENCODER_free(void* encoder) {
    (void)encoder;
}

static inline void* OSSL_DECODER_fetch(void* libctx, const char* name, const char* properties) {
    (void)libctx;
    (void)name;
    (void)properties;
    return NULL;
}

static inline void OSSL_DECODER_free(void* decoder) {
    (void)decoder;
}

/* Missing thread-related definitions */
#ifndef CRYPTO_ONCE
#define CRYPTO_ONCE int
#define CRYPTO_ONCE_STATIC_INIT 0
#endif

/* Thread-once stub implementation */
static inline int CRYPTO_THREAD_run_once(int* once, void (*init)(void)) {
    if (*once == 0) {
        init();
        *once = 1;
    }
    return 1;
}

/* Missing entropy definitions */
#ifndef RAND_DRBG_TYPE
#define RAND_DRBG_TYPE int
#define RAND_DRBG_CTR_HMAC 1
#endif

#endif /* __TEE_ENVIRONMENT__ */

#endif /* PROVIDER_COMPAT_H */