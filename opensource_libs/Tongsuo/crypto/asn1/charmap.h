/*
 * WARNING: do not edit!
 * Generated by crypto/asn1/charmap.pl
 *
 * Copyright 2000-2022 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#define CHARTYPE_HOST_ANY 4096
#define CHARTYPE_HOST_DOT 8192
#define CHARTYPE_HOST_HYPHEN 16384
#define CHARTYPE_HOST_WILD 32768

/*
 * Mask of various character properties
 */

static const unsigned short char_type[] = {
    1026,    2,    2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
       2,    2,    2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
       2,    2,    2,    2,    2,    2,    2,    2,  120,    0,    1,   40,
       0,    0,    0,   16, 1040, 1040, 33792,   25,   25, 16400, 8208,   16,
    4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112,   16,    9,
       9,   16,    9,   16,    0, 4112, 4112, 4112, 4112, 4112, 4112, 4112,
    4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112,
    4112, 4112, 4112, 4112, 4112, 4112, 4112,    0, 1025,    0,    0,    0,
       0, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112,
    4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112,
    4112, 4112, 4112,    0,    0,    0,    0,    2
};
