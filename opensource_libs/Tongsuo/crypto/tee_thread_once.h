/*
 * Copyright (C) 2024 The Tongsuo Project.
 *
 * TEE Thread-Once Implementation
 * 
 * This file provides thread-once functionality for TEE environment
 * where standard pthread_once is not available.
 */

#ifndef TEE_THREAD_ONCE_H
#define TEE_THREAD_ONCE_H

#ifdef __TEE_ENVIRONMENT__

/* TEE environment typically doesn't have full threading support */
#define OPENSSL_NO_THREADS 1

/* Define missing thread-related types and constants */
#ifndef CRYPTO_ONCE
typedef volatile int CRYPTO_ONCE;
#define CRYPTO_ONCE_STATIC_INIT 0
#endif

#ifndef CRYPTO_THREAD_LOCAL
#define CRYPTO_THREAD_LOCAL
#endif

#ifndef CRYPTO_THREAD_ID
typedef unsigned long CRYPTO_THREAD_ID;
#endif

#ifndef CRYPTO_RWLOCK  
typedef int CRYPTO_RWLOCK;
#endif

/* Simple thread-once implementation for TEE */
static inline int CRYPTO_THREAD_run_once(CRYPTO_ONCE *once, void (*init)(void))
{
    /* In TEE environment, we assume single-threaded execution for simplicity */
    if (*once == 0) {
        init();
        *once = 1;
    }
    return 1;
}

/* Thread ID functions */
static inline CRYPTO_THREAD_ID CRYPTO_THREAD_get_current_id(void)
{
    return 1; /* Simple stub for TEE */
}

static inline int CRYPTO_THREAD_compare_id(CRYPTO_THREAD_ID a, CRYPTO_THREAD_ID b)
{
    return (a == b);
}

/* Read-write lock stubs (single-threaded) */
static inline CRYPTO_RWLOCK *CRYPTO_THREAD_lock_new(void)
{
    static int dummy_lock = 0;
    return (CRYPTO_RWLOCK *)&dummy_lock;
}

static inline int CRYPTO_THREAD_read_lock(CRYPTO_RWLOCK *lock)
{
    (void)lock;
    return 1; /* Always success in single-threaded TEE */
}

static inline int CRYPTO_THREAD_write_lock(CRYPTO_RWLOCK *lock)
{
    (void)lock;
    return 1; /* Always success in single-threaded TEE */
}

static inline int CRYPTO_THREAD_unlock(CRYPTO_RWLOCK *lock)
{
    (void)lock;
    return 1; /* Always success */
}

static inline void CRYPTO_THREAD_lock_free(CRYPTO_RWLOCK *lock)
{
    (void)lock; /* No-op for static locks */
}

/* Thread local storage stubs */
static inline int CRYPTO_THREAD_init_local(void **key, void (*cleanup)(void *))
{
    (void)key;
    (void)cleanup;
    return 1; /* Success stub */
}

static inline void *CRYPTO_THREAD_get_local(void **key)
{
    (void)key;
    return NULL; /* No thread-local storage in TEE */
}

static inline int CRYPTO_THREAD_set_local(void **key, void *val)
{
    (void)key;
    (void)val;
    return 1; /* Success stub */
}

static inline int CRYPTO_THREAD_cleanup_local(void **key)
{
    (void)key;
    return 1; /* Success stub */
}

/* Atomic operations stubs */
#ifndef CRYPTO_atomic_add
static inline int CRYPTO_atomic_add(int *val, int amount, int *ret, CRYPTO_RWLOCK *lock)
{
    (void)lock;
    *val += amount;
    if (ret != NULL)
        *ret = *val;
    return 1;
}
#endif

#ifndef CRYPTO_atomic_read
static inline int CRYPTO_atomic_read(int *val, int *ret, CRYPTO_RWLOCK *lock)
{
    (void)lock;
    *ret = *val;
    return 1;
}
#endif

#ifndef CRYPTO_atomic_write
static inline int CRYPTO_atomic_write(int *val, int n, CRYPTO_RWLOCK *lock)
{
    (void)lock;
    *val = n;
    return 1;
}
#endif

/* Memory barrier stub */
#ifndef CRYPTO_atomic_or
static inline int CRYPTO_atomic_or(uint64_t *val, uint64_t op, uint64_t *ret, CRYPTO_RWLOCK *lock)
{
    (void)lock;
    *val |= op;
    if (ret != NULL)
        *ret = *val;
    return 1;
}
#endif

#ifndef CRYPTO_atomic_load
static inline int CRYPTO_atomic_load(uint64_t *val, uint64_t *ret, CRYPTO_RWLOCK *lock)
{
    (void)lock;
    *ret = *val;
    return 1;
}
#endif

/* Missing types and definitions for threading */
#ifndef OPENSSL_INIT_SETTINGS
typedef void OPENSSL_INIT_SETTINGS;
#endif

/* Initialize settings stubs */
static inline OPENSSL_INIT_SETTINGS *OPENSSL_INIT_new(void)
{
    return NULL; /* Use default settings */
}

static inline void OPENSSL_INIT_free(OPENSSL_INIT_SETTINGS *settings)
{
    (void)settings;
}

static inline int OPENSSL_INIT_set_config_filename(OPENSSL_INIT_SETTINGS *settings, const char *filename)
{
    (void)settings;
    (void)filename;
    return 1; /* Success stub - no config in TEE */
}

/* Missing cleanup function */
static inline void OPENSSL_thread_stop(void)
{
    /* No-op in TEE environment */
}

#endif /* __TEE_ENVIRONMENT__ */

#endif /* TEE_THREAD_ONCE_H */ 