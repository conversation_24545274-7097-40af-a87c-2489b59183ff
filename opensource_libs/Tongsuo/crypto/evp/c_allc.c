/*
 * Copyright 1995-2020 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#include <stdio.h>
#include "internal/cryptlib.h"
#include <openssl/evp.h>
#include "crypto/evp.h"
#include <openssl/pkcs12.h>
#include <openssl/objects.h>

void openssl_add_all_ciphers_int(void)
{

#ifndef OPENSSL_NO_DES
    EVP_add_cipher(EVP_des_cfb());
    EVP_add_cipher(EVP_des_cfb1());
    EVP_add_cipher(EVP_des_cfb8());
    EVP_add_cipher(EVP_des_ede_cfb());
    EVP_add_cipher(EVP_des_ede3_cfb());
    EVP_add_cipher(EVP_des_ede3_cfb1());
    EVP_add_cipher(EVP_des_ede3_cfb8());

    EVP_add_cipher(EVP_des_ofb());
    EVP_add_cipher(EVP_des_ede_ofb());
    EVP_add_cipher(EVP_des_ede3_ofb());

    EVP_add_cipher(EVP_desx_cbc());
    EVP_add_cipher_alias(SN_desx_cbc, "DESX");
    EVP_add_cipher_alias(SN_desx_cbc, "desx");

    EVP_add_cipher(EVP_des_cbc());
    EVP_add_cipher_alias(SN_des_cbc, "DES");
    EVP_add_cipher_alias(SN_des_cbc, "des");
    EVP_add_cipher(EVP_des_ede_cbc());
    EVP_add_cipher(EVP_des_ede3_cbc());
    EVP_add_cipher_alias(SN_des_ede3_cbc, "DES3");
    EVP_add_cipher_alias(SN_des_ede3_cbc, "des3");

    EVP_add_cipher(EVP_des_ecb());
    EVP_add_cipher(EVP_des_ede());
    EVP_add_cipher_alias(SN_des_ede_ecb, "DES-EDE-ECB");
    EVP_add_cipher_alias(SN_des_ede_ecb, "des-ede-ecb");
    EVP_add_cipher(EVP_des_ede3());
    EVP_add_cipher_alias(SN_des_ede3_ecb, "DES-EDE3-ECB");
    EVP_add_cipher_alias(SN_des_ede3_ecb, "des-ede3-ecb");
    EVP_add_cipher(EVP_des_ede3_wrap());
    EVP_add_cipher_alias(SN_id_smime_alg_CMS3DESwrap, "des3-wrap");
#endif

#ifndef OPENSSL_NO_RC4
    EVP_add_cipher(EVP_rc4());
    EVP_add_cipher(EVP_rc4_40());
# ifndef OPENSSL_NO_MD5
    EVP_add_cipher(EVP_rc4_hmac_md5());
# endif
#endif

#ifndef OPENSSL_NO_SM4
    EVP_add_cipher(EVP_sm4_ecb());
    EVP_add_cipher(EVP_sm4_cbc());
    EVP_add_cipher(EVP_sm4_cfb());
    EVP_add_cipher(EVP_sm4_ofb());
    EVP_add_cipher(EVP_sm4_ctr());
    EVP_add_cipher_alias(SN_sm4_cbc, "SM4");
    EVP_add_cipher_alias(SN_sm4_cbc, "sm4");
    EVP_add_cipher(EVP_sm4_gcm());
    EVP_add_cipher(EVP_sm4_ccm());
#endif

#ifndef OPENSSL_NO_WBSM4_XIAOLAI
    EVP_add_cipher(EVP_wbsm4_xiaolai_ecb());
    EVP_add_cipher(EVP_wbsm4_xiaolai_cbc());
    EVP_add_cipher(EVP_wbsm4_xiaolai_cfb());
    EVP_add_cipher(EVP_wbsm4_xiaolai_ofb());
    EVP_add_cipher(EVP_wbsm4_xiaolai_ctr());
    EVP_add_cipher_alias(SN_wbsm4_xiaolai_cbc, "WBSM4-XIAOLAI");
    EVP_add_cipher_alias(SN_wbsm4_xiaolai_cbc, "wbsm4-xiaolai");
    EVP_add_cipher(EVP_wbsm4_xiaolai_gcm());
    EVP_add_cipher(EVP_wbsm4_xiaolai_ccm());
#endif

#ifndef OPENSSL_NO_WBSM4_BAIWU
    EVP_add_cipher(EVP_wbsm4_baiwu_ecb());
    EVP_add_cipher(EVP_wbsm4_baiwu_cbc());
    EVP_add_cipher(EVP_wbsm4_baiwu_cfb());
    EVP_add_cipher(EVP_wbsm4_baiwu_ofb());
    EVP_add_cipher(EVP_wbsm4_baiwu_ctr());
    EVP_add_cipher_alias(SN_wbsm4_baiwu_cbc, "WBSM4-BAIWU");
    EVP_add_cipher_alias(SN_wbsm4_baiwu_cbc, "wbsm4-baiwu");
    EVP_add_cipher(EVP_wbsm4_baiwu_gcm());
    EVP_add_cipher(EVP_wbsm4_baiwu_ccm());
#endif

#ifndef OPENSSL_NO_WBSM4_WSISE
    EVP_add_cipher(EVP_wbsm4_wsise_ecb());
    EVP_add_cipher(EVP_wbsm4_wsise_cbc());
    EVP_add_cipher(EVP_wbsm4_wsise_cfb());
    EVP_add_cipher(EVP_wbsm4_wsise_ofb());
    EVP_add_cipher(EVP_wbsm4_wsise_ctr());
    EVP_add_cipher_alias(SN_wbsm4_wsise_cbc, "WBSM4-WSISE");
    EVP_add_cipher_alias(SN_wbsm4_wsise_cbc, "wbsm4-wsise");
    EVP_add_cipher(EVP_wbsm4_wsise_gcm());
    EVP_add_cipher(EVP_wbsm4_wsise_ccm());
#endif

#ifndef OPENSSL_NO_RC5
    EVP_add_cipher(EVP_rc5_32_12_16_ecb());
    EVP_add_cipher(EVP_rc5_32_12_16_cfb());
    EVP_add_cipher(EVP_rc5_32_12_16_ofb());
    EVP_add_cipher(EVP_rc5_32_12_16_cbc());
    EVP_add_cipher_alias(SN_rc5_cbc, "rc5");
    EVP_add_cipher_alias(SN_rc5_cbc, "RC5");
#endif

    EVP_add_cipher(EVP_aes_128_ecb());
    EVP_add_cipher(EVP_aes_128_cbc());
    EVP_add_cipher(EVP_aes_128_cfb());
    EVP_add_cipher(EVP_aes_128_cfb1());
    EVP_add_cipher(EVP_aes_128_cfb8());
    EVP_add_cipher(EVP_aes_128_ofb());
    EVP_add_cipher(EVP_aes_128_ctr());
    EVP_add_cipher(EVP_aes_128_gcm());
#ifndef OPENSSL_NO_OCB
    EVP_add_cipher(EVP_aes_128_ocb());
#endif
    EVP_add_cipher(EVP_aes_128_xts());
    EVP_add_cipher(EVP_aes_128_ccm());
    EVP_add_cipher(EVP_aes_128_wrap());
    EVP_add_cipher_alias(SN_id_aes128_wrap, "aes128-wrap");
    EVP_add_cipher(EVP_aes_128_wrap_pad());
    EVP_add_cipher_alias(SN_aes_128_cbc, "AES128");
    EVP_add_cipher_alias(SN_aes_128_cbc, "aes128");
    EVP_add_cipher(EVP_aes_192_ecb());
    EVP_add_cipher(EVP_aes_192_cbc());
    EVP_add_cipher(EVP_aes_192_cfb());
    EVP_add_cipher(EVP_aes_192_cfb1());
    EVP_add_cipher(EVP_aes_192_cfb8());
    EVP_add_cipher(EVP_aes_192_ofb());
    EVP_add_cipher(EVP_aes_192_ctr());
    EVP_add_cipher(EVP_aes_192_gcm());
#ifndef OPENSSL_NO_OCB
    EVP_add_cipher(EVP_aes_192_ocb());
#endif
    EVP_add_cipher(EVP_aes_192_ccm());
    EVP_add_cipher(EVP_aes_192_wrap());
    EVP_add_cipher_alias(SN_id_aes192_wrap, "aes192-wrap");
    EVP_add_cipher(EVP_aes_192_wrap_pad());
    EVP_add_cipher_alias(SN_aes_192_cbc, "AES192");
    EVP_add_cipher_alias(SN_aes_192_cbc, "aes192");
    EVP_add_cipher(EVP_aes_256_ecb());
    EVP_add_cipher(EVP_aes_256_cbc());
    EVP_add_cipher(EVP_aes_256_cfb());
    EVP_add_cipher(EVP_aes_256_cfb1());
    EVP_add_cipher(EVP_aes_256_cfb8());
    EVP_add_cipher(EVP_aes_256_ofb());
    EVP_add_cipher(EVP_aes_256_ctr());
    EVP_add_cipher(EVP_aes_256_gcm());
#ifndef OPENSSL_NO_OCB
    EVP_add_cipher(EVP_aes_256_ocb());
#endif
    EVP_add_cipher(EVP_aes_256_xts());
    EVP_add_cipher(EVP_aes_256_ccm());
    EVP_add_cipher(EVP_aes_256_wrap());
    EVP_add_cipher_alias(SN_id_aes256_wrap, "aes256-wrap");
    EVP_add_cipher(EVP_aes_256_wrap_pad());
    EVP_add_cipher_alias(SN_aes_256_cbc, "AES256");
    EVP_add_cipher_alias(SN_aes_256_cbc, "aes256");
    EVP_add_cipher(EVP_aes_128_cbc_hmac_sha1());
    EVP_add_cipher(EVP_aes_256_cbc_hmac_sha1());
    EVP_add_cipher(EVP_aes_128_cbc_hmac_sha256());
    EVP_add_cipher(EVP_aes_256_cbc_hmac_sha256());

#ifndef OPENSSL_NO_CHACHA
    EVP_add_cipher(EVP_chacha20());
# ifndef OPENSSL_NO_POLY1305
    EVP_add_cipher(EVP_chacha20_poly1305());
# endif
#endif

#ifndef OPENSSL_NO_ZUC
    EVP_add_cipher(EVP_eea3());
#endif
}
