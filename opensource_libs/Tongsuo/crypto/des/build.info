$DESASM=des_enc.c fcrypt_b.c
IF[{- !$disabled{asm} -}]
  $DESASM_x86=des-586.s crypt586.s

  # Now that we have defined all the arch specific variables, use the
  # appropriate one
  IF[$DESASM_{- $target{asm_arch} -}]
    $DESASM=$DESASM_{- $target{asm_arch} -}
    $DESDEF=DES_ASM
  ENDIF
ENDIF

LIBS=../../libcrypto
$COMMON=set_key.c ecb3_enc.c
$ALL=$COMMON\
     ecb_enc.c  cbc_enc.c \
     cfb64enc.c cfb64ede.c cfb_enc.c \
     ofb64ede.c ofb64enc.c ofb_enc.c \
     str2key.c  pcbc_enc.c qud_cksm.c rand_key.c \
     fcrypt.c xcbc_enc.c cbc_cksm.c

SOURCE[../../libcrypto]=$ALL $DESASM
SOURCE[../../providers/libfips.a]=$COMMON $DESASM
SOURCE[../../providers/liblegacy.a]=$DESASM

DEFINE[../../libcrypto]=$DESDEF
DEFINE[../../providers/libfips.a]=$DESDEF
DEFINE[../../providers/liblegacy.a]=$DESDEF

# When all deprecated symbols are removed, libcrypto doesn't export the
# DES functions, so we must include them directly in liblegacy.a
IF[{- $disabled{'deprecated-3.0'} && !$disabled{des} -}]
  SOURCE[../../providers/liblegacy.a]=$ALL
  DEFINE[../../providers/liblegacy.a]=$DESDEF
ENDIF

GENERATE[des-586.s]=asm/des-586.pl
DEPEND[des-586.s]=../perlasm/x86asm.pl ../perlasm/cbc.pl
GENERATE[crypt586.s]=asm/crypt586.pl
DEPEND[crypt586.s]=../perlasm/x86asm.pl ../perlasm/cbc.pl
