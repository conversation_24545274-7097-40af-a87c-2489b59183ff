# Tongsuo Build Configuration - Extended Algorithm Support
LOCAL_DIR := $(GET_LOCAL_DIR)
MODULE := $(LOCAL_DIR)

$(info ====== Tongsuo Build (Extended Algorithms) ======)

MODULE_DEFINES += \
OPENSSL_BUILDING_OPENSSL=1 \
OPENSSL_THREADS=0 \
OPENSSL_SM2=1 \
OPENSSL_SM3=1 \
OPENSSL_SM4=1 \
OPENSSL_AES=1 \
OPENSSL_RSA=1 \
OPENSSL_SHA=1 \
OPENSSL_EC=1 \
OPENSSL_EVP=1 \
OPENSSL_BN=1 \
OPENSSL_DES=1 \
OPENSSL_MD5=1 \
OPENSSL_HMAC=1 \
OPENSSL_RAND=1 \
__TEE_ENVIRONMENT__=1

# Only local includes - NO global export
MODULE_INCLUDES += \
$(LOCAL_DIR) \
$(LOCAL_DIR)/include \
$(LOCAL_DIR)/include/openssl

MODULE_CFLAGS += \
-D__TEE_ENVIRONMENT__=1 \
-DTONGSUO_BUILDING=1 \
-D__GNUC_PREREQ\(x,y\)=0 \
-D__glibc_clang_prereq\(x,y\)=0 \
-DOPENSSL_NO_DEPRECATED_3_0=1 \
-DOPENSSL_NO_PROVIDER=1

# Core crypto infrastructure (simplified for TEE environment)
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/cryptlib.c \
$(LOCAL_DIR)/crypto/mem.c \
$(LOCAL_DIR)/crypto/mem_clr.c \
$(LOCAL_DIR)/crypto/ex_data.c \
$(LOCAL_DIR)/crypto/cversion.c \
$(LOCAL_DIR)/crypto/params.c \
$(LOCAL_DIR)/crypto/bsearch.c \
$(LOCAL_DIR)/crypto/context.c \
$(LOCAL_DIR)/crypto/sparse_array.c \
$(LOCAL_DIR)/crypto/threads_none.c

# Error handling (simplified)
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/err/err.c

# Buffer management
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/buffer/buffer.c \
$(LOCAL_DIR)/crypto/buffer/buf_err.c

# Stack operations
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/stack/stack.c

# SM algorithms (Chinese national standards) - simplified
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/sm2/sm2_crypt.c \
$(LOCAL_DIR)/crypto/sm2/sm2_sign.c \
$(LOCAL_DIR)/crypto/sm2/sm2_key.c \
$(LOCAL_DIR)/crypto/sm3/sm3.c \
$(LOCAL_DIR)/crypto/sm4/sm4.c

# AES algorithm (based on Tongsuo build.info)
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/aes/aes_misc.c \
$(LOCAL_DIR)/crypto/aes/aes_ecb.c \
$(LOCAL_DIR)/crypto/aes/aes_core.c \
$(LOCAL_DIR)/crypto/aes/aes_cbc.c \
$(LOCAL_DIR)/crypto/aes/aes_cfb.c \
$(LOCAL_DIR)/crypto/aes/aes_ofb.c \
$(LOCAL_DIR)/crypto/aes/aes_wrap.c

# SHA algorithms (based on Tongsuo build.info)
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/sha/sha1dgst.c \
$(LOCAL_DIR)/crypto/sha/sha1_one.c \
$(LOCAL_DIR)/crypto/sha/sha256.c \
$(LOCAL_DIR)/crypto/sha/sha512.c

# RSA algorithm (minimal for TEE environment)
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/rsa/rsa_lib.c

# Big Number (BN) support (simplified for TEE environment)
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/bn/bn_lib.c \
$(LOCAL_DIR)/crypto/bn/bn_ctx.c \
$(LOCAL_DIR)/crypto/bn/bn_add.c \
$(LOCAL_DIR)/crypto/bn/bn_mul.c \
$(LOCAL_DIR)/crypto/bn/bn_div.c \
$(LOCAL_DIR)/crypto/bn/bn_mod.c \
$(LOCAL_DIR)/crypto/bn/bn_exp.c \
$(LOCAL_DIR)/crypto/bn/bn_word.c \
$(LOCAL_DIR)/crypto/bn/bn_rand.c \
$(LOCAL_DIR)/crypto/bn/bn_shift.c \
$(LOCAL_DIR)/crypto/bn/bn_sqr.c \
$(LOCAL_DIR)/crypto/bn/bn_gcd.c \
$(LOCAL_DIR)/crypto/bn/bn_prime.c \
$(LOCAL_DIR)/crypto/bn/bn_err.c \
$(LOCAL_DIR)/crypto/bn/bn_recp.c \
$(LOCAL_DIR)/crypto/bn/bn_mont.c \
$(LOCAL_DIR)/crypto/bn/bn_mpi.c \
$(LOCAL_DIR)/crypto/bn/bn_exp2.c

# EVP (high-level interface) - based on Tongsuo build.info
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/evp/digest.c \
$(LOCAL_DIR)/crypto/evp/evp_enc.c \
$(LOCAL_DIR)/crypto/evp/evp_lib.c \
$(LOCAL_DIR)/crypto/evp/evp_fetch.c \
$(LOCAL_DIR)/crypto/evp/evp_utils.c \
$(LOCAL_DIR)/crypto/evp/names.c \
$(LOCAL_DIR)/crypto/evp/evp_err.c \
$(LOCAL_DIR)/crypto/evp/e_aes.c \
$(LOCAL_DIR)/crypto/evp/e_des.c \
$(LOCAL_DIR)/crypto/evp/e_des3.c \
$(LOCAL_DIR)/crypto/evp/e_sm4.c \
$(LOCAL_DIR)/crypto/evp/e_null.c \
$(LOCAL_DIR)/crypto/evp/legacy_sha.c

# Random number generation (simplified)
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/rand/rand_err.c

# MD5 algorithm
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/md5/md5_dgst.c \
$(LOCAL_DIR)/crypto/md5/md5_one.c

# HMAC algorithm (based on Tongsuo build.info)
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/hmac/hmac.c

# DES algorithm
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/des/des_enc.c \
$(LOCAL_DIR)/crypto/des/fcrypt_b.c \
$(LOCAL_DIR)/crypto/des/set_key.c \
$(LOCAL_DIR)/crypto/des/ecb_enc.c \
$(LOCAL_DIR)/crypto/des/cbc_enc.c \
$(LOCAL_DIR)/crypto/des/cfb64enc.c \
$(LOCAL_DIR)/crypto/des/cfb_enc.c \
$(LOCAL_DIR)/crypto/des/ofb64enc.c \
$(LOCAL_DIR)/crypto/des/ofb_enc.c

# Object management
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/objects/obj_dat.c \
$(LOCAL_DIR)/crypto/objects/obj_lib.c \
$(LOCAL_DIR)/crypto/objects/obj_err.c

include make/rctee_lib.mk
