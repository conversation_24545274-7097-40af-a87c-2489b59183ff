# Tongsuo Build Configuration - Extended Algorithm Support
LOCAL_DIR := $(GET_LOCAL_DIR)
MODULE := $(LOCAL_DIR)

$(info ====== Tongsuo Build (Extended Algorithms) ======)

MODULE_DEFINES += \
OPENSSL_BUILDING_OPENSSL=1 \
OPENSSL_THREADS=0 \
OPENSSL_SM2=1 \
OPENSSL_SM3=1 \
OPENSSL_SM4=1 \
OPENSSL_AES=1 \
OPENSSL_RSA=1 \
OPENSSL_SHA=1 \
OPENSSL_EC=1 \
OPENSSL_EVP=1 \
OPENSSL_BN=1 \
OPENSSL_DES=1 \
OPENSSL_MD5=1 \
OPENSSL_HMAC=1 \
OPENSSL_RAND=1 \
__TEE_ENVIRONMENT__=1

# Only local includes - NO global export
MODULE_INCLUDES += \
$(LOCAL_DIR) \
$(LOCAL_DIR)/include \
$(LOCAL_DIR)/include/openssl

MODULE_CFLAGS += \
-D__TEE_ENVIRONMENT__=1 \
-DTONGSUO_BUILDING=1

# Core crypto infrastructure
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/cryptlib.c \
$(LOCAL_DIR)/crypto/mem.c \
$(LOCAL_DIR)/crypto/err/err.c \
$(LOCAL_DIR)/crypto/ex_data.c

# SM algorithms (Chinese national standards)
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/sm2/sm2_crypt.c \
$(LOCAL_DIR)/crypto/sm2/sm2_sign.c \
$(LOCAL_DIR)/crypto/sm2/sm2_key.c \
$(LOCAL_DIR)/crypto/sm3/sm3.c \
$(LOCAL_DIR)/crypto/sm4/sm4.c

# AES algorithm
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/aes/aes_core.c \
$(LOCAL_DIR)/crypto/aes/aes_cbc.c \
$(LOCAL_DIR)/crypto/aes/aes_cfb.c \
$(LOCAL_DIR)/crypto/aes/aes_ecb.c \
$(LOCAL_DIR)/crypto/aes/aes_ofb.c \
$(LOCAL_DIR)/crypto/aes/aes_misc.c

# SHA algorithms
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/sha/sha1dgst.c \
$(LOCAL_DIR)/crypto/sha/sha1_one.c \
$(LOCAL_DIR)/crypto/sha/sha256.c \
$(LOCAL_DIR)/crypto/sha/sha512.c

# RSA algorithm
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/rsa/rsa_lib.c \
$(LOCAL_DIR)/crypto/rsa/rsa_gen.c \
$(LOCAL_DIR)/crypto/rsa/rsa_sign.c \
$(LOCAL_DIR)/crypto/rsa/rsa_ossl.c \
$(LOCAL_DIR)/crypto/rsa/rsa_pk1.c \
$(LOCAL_DIR)/crypto/rsa/rsa_none.c

# Big Number (BN) support
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/bn/bn_lib.c \
$(LOCAL_DIR)/crypto/bn/bn_ctx.c \
$(LOCAL_DIR)/crypto/bn/bn_add.c \
$(LOCAL_DIR)/crypto/bn/bn_sub.c \
$(LOCAL_DIR)/crypto/bn/bn_mul.c \
$(LOCAL_DIR)/crypto/bn/bn_div.c \
$(LOCAL_DIR)/crypto/bn/bn_mod.c \
$(LOCAL_DIR)/crypto/bn/bn_exp.c \
$(LOCAL_DIR)/crypto/bn/bn_word.c \
$(LOCAL_DIR)/crypto/bn/bn_rand.c

# EVP (high-level interface)
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/evp/evp_lib.c \
$(LOCAL_DIR)/crypto/evp/digest.c \
$(LOCAL_DIR)/crypto/evp/evp_enc.c \
$(LOCAL_DIR)/crypto/evp/names.c \
$(LOCAL_DIR)/crypto/evp/m_sha1.c \
$(LOCAL_DIR)/crypto/evp/m_sha256.c \
$(LOCAL_DIR)/crypto/evp/m_sha512.c \
$(LOCAL_DIR)/crypto/evp/m_sm3.c

# Random number generation
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/rand/rand_lib.c \
$(LOCAL_DIR)/crypto/rand/rand_pool.c

# Buffer management
MODULE_SRCS += \
$(LOCAL_DIR)/crypto/buffer/buffer.c

include make/rctee_lib.mk
