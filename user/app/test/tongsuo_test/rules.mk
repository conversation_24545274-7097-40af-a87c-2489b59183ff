# Tongsuo Test TA - 正确的库依赖方式

LOCAL_DIR := $(GET_LOCAL_DIR)
MODULE := $(LOCAL_DIR)

MANIFEST := $(LOCAL_DIR)/manifest.json

# 正确使用 MODULE_LIBRARY_DEPS 导入 Tongsuo 库
MODULE_LIBRARY_DEPS += \
opensource_libs/Tongsuo \
user/base/lib/libc-rctee

# Add Tongsuo headers ONLY for this TA
MODULE_INCLUDES += \
opensource_libs/Tongsuo/include \
opensource_libs/Tongsuo/include/openssl

# Define required macros for Tongsuo-only TA
MODULE_CFLAGS += \
-DTONGSUO_ONLY_TA=1 \
-DTONGSUO_EXCLUDE_BORINGSSL=1

MODULE_SRCS := \
$(LOCAL_DIR)/main.c

include make/ta.mk
