# Tongsuo Integration for Trusty TEE

## Overview

This document describes the integration of Tongsuo (铜锁) cryptographic library into the Trusty TEE environment with seamless coexistence alongside BoringSSL.

## What is Tongsuo?

Tongsuo is an open-source cryptographic library that provides:
- **Chinese National Cryptographic Standards**: SM2, SM3, SM4
- **NTLS Protocol**: National Transport Layer Security
- **Zero-Knowledge Proofs**: Advanced cryptographic protocols
- **International Standards**: Full OpenSSL API compatibility

Official website: https://www.tongsuo.net/

## Integration Architecture

### Coexistence Strategy

The integration supports multiple deployment modes:

#### 1. PRESERVE_BORINGSSL Mode (Default with BoringSSL present)
- Existing TAs continue using BoringSSL unchanged
- Tongsuo available via explicit namespace: `tongsuo/`
- Symbol prefixing prevents conflicts
- Zero risk to existing functionality

#### 2. TONGSUO_PREFERRED Mode (Without BoringSSL)
- Tongsuo becomes the default cryptographic provider
- Clean integration without legacy conflicts
- Full access to Chinese cryptographic standards

### Symbol Management

When BoringSSL is present, symbol prefixing ensures no conflicts:
```c
// BoringSSL (existing code unchanged)
#include <openssl/evp.h>
EVP_DigestInit(...);  // -> BoringSSL implementation

// Tongsuo (explicit namespace)
#include <tongsuo/evp.h>
tongsuo_EVP_DigestInit(...);  // -> Tongsuo implementation
```

## Build Configuration

### Prerequisites

1. Tongsuo source code in `opensource_libs/Tongsuo/`
2. Cross-compilation toolchain
3. TEE build environment

### Build Process

```bash
# Set up environment
export COMPILER_PATH=/path/to/cross-compiler
export BUILD_TARGET=imx8mp

# Run integration build
./build_tongsuo_integration.sh
```

### Configuration Files

#### Primary Build Rules
- `opensource_libs/Tongsuo/rules.mk`: Main build configuration
- Automatic BoringSSL detection and coexistence setup
- TEE-specific optimizations and feature disables

#### Migration Support Headers
- `include/tongsuo/migration_strategy.h`: Migration mode definitions
- `include/tongsuo/openssl_wrapper.h`: API compatibility layer
- `crypto/provider_compat.h`: TEE provider compatibility
- `crypto/tee_thread_once.h`: Thread synchronization stubs
- `crypto/o_str_patch.h`: TEE environment patches

## Test Application: tongsuo_test

### Purpose
The `tongsuo_test` TA serves multiple purposes:
1. **Force Tongsuo Inclusion**: Ensures Tongsuo is compiled into system build
2. **Algorithm Validation**: Tests Chinese and international cryptographic algorithms
3. **Integration Verification**: Confirms Tongsuo works correctly in TEE environment

### Configuration
```makefile
# Tongsuo-only configuration
MODULE_CFLAGS += -DTONGSUO_ONLY_TA=1
MODULE_CFLAGS += -DTONGSUO_EXCLUDE_BORINGSSL=1

# Library dependencies - Tongsuo only
MODULE_LIBRARY_DEPS := \
    user/base/lib/libc-rctee \
    opensource_libs/Tongsuo
```

### Usage Example
```c
#include <openssl/evp.h>    // Tongsuo implementation
#include <openssl/sm3.h>    // Chinese SM3 hash
#include <openssl/sm4.h>    // Chinese SM4 cipher

// All algorithms route through Tongsuo
EVP_DigestInit_ex(&ctx, EVP_sm3(), NULL);
```

## Migration Strategies

### Strategy 1: Preserve Existing Code (PRESERVE_BORINGSSL)
```c
// Existing TA code (unchanged)
#include <openssl/evp.h>
EVP_DigestInit(&ctx, EVP_sha256(), NULL);  // -> BoringSSL

// New Chinese crypto features
#include <tongsuo/sm3.h>
tongsuo_EVP_DigestInit(&ctx, tongsuo_EVP_sm3(), NULL);  // -> Tongsuo
```

### Strategy 2: Gradual Migration (GRADUAL_MIGRATION)
```c
// Per-module migration configuration
#define MODULE_CRYPTO_PROVIDER TONGSUO

// Same API, different backend
#include <openssl/evp.h>
EVP_DigestInit(&ctx, EVP_sha256(), NULL);  // -> Tongsuo (via wrapper)
```

### Strategy 3: Full Migration (TONGSUO_PREFERRED)
```c
// Default to Tongsuo for all operations
#include <openssl/evp.h>    // -> Tongsuo
#include <openssl/sm3.h>    // -> Chinese algorithms available
```

## Supported Algorithms

### Chinese National Standards (Tongsuo Exclusive)
- **SM2**: Elliptic curve public key cryptography
- **SM3**: Cryptographic hash function
- **SM4**: Block cipher

### International Standards (via Tongsuo)
- **RSA**: Public key cryptography
- **AES**: Advanced Encryption Standard
- **SHA**: Secure Hash Algorithms (SHA-1, SHA-256, SHA-512)
- **ECDSA**: Elliptic Curve Digital Signature Algorithm

### Protocols
- **NTLS**: National Transport Layer Security (Tongsuo exclusive)
- **ZKP**: Zero-Knowledge Proof protocols (Tongsuo exclusive)

## TEE Environment Adaptations

### Disabled Features
Tongsuo is configured for TEE environment with these disabled:
- Network I/O (`no-sock`, `no-dgram`)
- File system (`no-stdio`, `no-posix-io`)
- Dynamic loading (`no-dso`, `no-engine`)
- Threading (`no-threads`)
- SSL/TLS protocols (`no-ssl`, `no-tls`)

### TEE-Specific Patches
- `o_str_patch.h`: String and entropy function stubs
- `provider_compat.h`: Provider system compatibility
- `tee_thread_once.h`: Thread synchronization for single-threaded TEE
- Atomic operations disabled (TEE typically single-threaded)

## Development Guidelines

### For New TAs Using Chinese Cryptography
```c
// Use Tongsuo exclusively
#define MODULE_CRYPTO_PROVIDER TONGSUO

#include <openssl/sm2.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>

// Standard OpenSSL APIs work with Chinese algorithms
EVP_DigestInit_ex(&ctx, EVP_sm3(), NULL);
```

### For Existing TAs (BoringSSL Migration)
```c
// Option 1: Keep using BoringSSL (no changes required)
#include <openssl/evp.h>  // -> BoringSSL

// Option 2: Migrate specific algorithms to Tongsuo
#include <tongsuo/sm3.h>  // -> Chinese algorithms via Tongsuo
#include <openssl/evp.h>  // -> Other algorithms via BoringSSL
```

### Best Practices
1. **Start with tongsuo_test**: Use as reference implementation
2. **Incremental adoption**: Begin with Chinese algorithms only
3. **Test thoroughly**: Validate all cryptographic operations
4. **Monitor binary size**: TEE environments have space constraints

## Troubleshooting

### Build Issues

#### "Tongsuo not found"
```bash
# Ensure Tongsuo source is present
ls opensource_libs/Tongsuo/

# Check build configuration
make tongsuo-info
```

#### "Symbol conflicts"
```bash
# Verify symbol prefixing is enabled
grep "TONGSUO_SYMBOL_PREFIX" opensource_libs/Tongsuo/rules.mk

# Check for BoringSSL presence
test -d external/boringssl && echo "BoringSSL present"
```

### Runtime Issues

#### "Algorithm not found"
- Ensure algorithm is enabled in Tongsuo configuration
- Check if TEE environment disables specific features
- Verify provider compatibility layer

#### "Memory allocation failures"
- TEE environments have limited memory
- Consider reducing Tongsuo feature set
- Monitor heap usage

## Performance Considerations

### Binary Size Impact
- Tongsuo adds ~500KB to binary size
- Chinese algorithms add minimal overhead
- International algorithms may duplicate BoringSSL functionality

### Runtime Performance
- Chinese algorithms (SM2/3/4) optimized for Chinese hardware
- International algorithms comparable to BoringSSL
- TEE environment may limit optimization opportunities

### Memory Usage
- Static allocation preferred in TEE
- Provider system disabled to reduce memory footprint
- Algorithm-specific optimizations enabled

## Security Considerations

### Cryptographic Compliance
- SM algorithms certified for Chinese national standards
- NTLS protocol for secure communications
- Formal verification for critical cryptographic operations

### TEE Integration
- Secure boot integration maintained
- Trusted application isolation preserved
- Hardware security module (HSM) compatibility

### Attack Surface
- Provider system disabled reduces attack surface
- Dynamic loading disabled for security
- Minimal network/file system interaction

## Migration Timeline

### Phase 1: Foundation (Current)
- ✅ Basic Tongsuo integration
- ✅ Chinese algorithm support
- ✅ BoringSSL coexistence
- ✅ Test application (tongsuo_test)

### Phase 2: Validation
- 🔄 Comprehensive testing
- 🔄 Performance benchmarking
- 🔄 Security audit
- 🔄 Production deployment

### Phase 3: Adoption
- ⏳ New TA development with Chinese algorithms
- ⏳ Gradual migration of existing TAs
- ⏳ Full Chinese cryptographic compliance

## Support and Resources

### Documentation
- [Tongsuo Official Documentation](https://www.tongsuo.net/docs/)
- [Chinese Cryptographic Standards](http://www.gmbz.org.cn/)
- [TEE Integration Guide](./MIGRATION_SUMMARY.md)

### Community
- Tongsuo GitHub: https://github.com/Tongsuo-Project/Tongsuo
- Issue reporting: Use GitHub issues for Tongsuo-specific problems
- TEE integration: Contact TEE platform maintainers

---

**Generated by:** Tongsuo Integration Team  
**Last Updated:** 2024  
**Version:** 1.0 