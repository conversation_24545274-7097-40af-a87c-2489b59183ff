Creating out folder
make[1]: Entering directory '/home/<USER>/codebase/trusty-tee'
make[2]: Entering directory '/home/<USER>/codebase/trusty-tee'
PROJECT = imx8mp
PLATFORM = imx
TARGET = imx8mp
EXTRA_BUILDRULES: make/all_task.mk
TOOLCHAIN_PREFIX = /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/llvm-
including-incmodules app dev dev/interrupt/arm_gic dev/timer/arm_generic kernel kernel/rctee/app/busytest kernel/rctee/lib/ktipc/test/main kernel/rctee/lib/ktipc/test/srv kernel/rctee/lib/memlog kernel/rctee/lib/rctee kernel/rctee/lib/sm kernel/rctee/lib/ubsan kernel/rctee/lib/version kernel/rctee/services/apploader kernel/rctee/services/generic_ta_service kernel/rctee/services/hwrng kernel/rctee/services/smc platform target
including-incmodules build/tools/package_tool kernel/rctee/lib/app_manifest kernel/rctee/lib/arm_ffa kernel/rctee/lib/backtrace kernel/rctee/lib/extmem kernel/rctee/lib/ktipc kernel/rctee/lib/libc-ext kernel/rctee/lib/libc-trusty kernel/rctee/lib/rand kernel/rctee/lib/smc kernel/rctee/lib/syscall kernel/rctee/lib/unittest kernel/vm lib/debug lib/fixed_point lib/heap user/base/app/apploader/tests/cbor_test
RCTEE_LIBC_RANDSEED = 0xf8c51054U
including-incmodules lib/binary_search_tree lib/heap/miniheap lib/io
including-incmodules lib/cbuf
Parsing RCTEE userspace tasks
make/generic_compile.mk:68: warning: overriding recipe for target 'out/build-imx8mp/host_tools/obj/apploader_package_tool/opensource_libs/open-dice/src/cbor_reader.o'
make/generic_compile.mk:68: warning: ignoring old recipe for target 'out/build-imx8mp/host_tools/obj/apploader_package_tool/opensource_libs/open-dice/src/cbor_reader.o'
make/generic_compile.mk:68: warning: overriding recipe for target 'out/build-imx8mp/host_tools/obj/apploader_package_tool/opensource_libs/open-dice/src/cbor_writer.o'
make/generic_compile.mk:68: warning: ignoring old recipe for target 'out/build-imx8mp/host_tools/obj/apploader_package_tool/opensource_libs/open-dice/src/cbor_writer.o'
make/generic_compile.mk:78: warning: overriding recipe for target 'out/build-imx8mp/host_tools/obj/apploader_package_tool/build/tools/package_tool/apploader_package_tool.o'
make/generic_compile.mk:78: warning: ignoring old recipe for target 'out/build-imx8mp/host_tools/obj/apploader_package_tool/build/tools/package_tool/apploader_package_tool.o'
make/generic_compile.mk:78: warning: overriding recipe for target 'out/build-imx8mp/host_tools/obj/apploader_package_tool/user/base/app/apploader/app_manifest_parser.o'
make/generic_compile.mk:78: warning: ignoring old recipe for target 'out/build-imx8mp/host_tools/obj/apploader_package_tool/user/base/app/apploader/app_manifest_parser.o'
make/generic_compile.mk:78: warning: overriding recipe for target 'out/build-imx8mp/host_tools/obj/apploader_package_tool/user/base/lib/apploader_package/cose.o'
make/generic_compile.mk:78: warning: ignoring old recipe for target 'out/build-imx8mp/host_tools/obj/apploader_package_tool/user/base/lib/apploader_package/cose.o'
make/host_tool.mk:95: warning: overriding recipe for target 'out/build-imx8mp/host_tools/apploader_package_tool'
make/host_tool.mk:95: warning: ignoring old recipe for target 'out/build-imx8mp/host_tools/apploader_package_tool'
ALL_TA_TASKS: kernel/hardware/nxp/app/hwcrypto user/app/sample/manifest-test user/app/sample/memref-test/receiver user/app/test/tongsuo_test user/base/app/apploader user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_1
Parsing Compnent: kernel/hardware/nxp/app/hwcrypto
kernel/hardware/nxp/app/hwcrypto is lk build form, just be compatible with it!
generating manifest for kernel/hardware/nxp/app/hwcrypto: out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.manifest
kernel/hardware/nxp/app/hwcrypto variables will all be reset!
TA_COMP_OUTDIR: out/build-imx8mp/user_product/ta/hwcrypto
TA_LDFLAGS:  -z max-page-size=4096 -z separate-loadable-segments --undefined=__aeabi_unwind_cpp_pr0 --gc-sections -static -pie --no-dynamic-linker -z text -Bsymbolic  -Lout/build-imx8mp/user_product/lib/boringssl  -Lout/build-imx8mp/user_product/lib/dlmalloc  -Lout/build-imx8mp/user_product/lib/hwkey  -Lout/build-imx8mp/user_product/lib/libc-ext  -Lout/build-imx8mp/user_product/lib/libc-rctee  -Lout/build-imx8mp/user_product/lib/libcxxabi-trusty  -Lout/build-imx8mp/user_product/lib/libstdc++-trusty  -Lout/build-imx8mp/user_product/lib/line-coverage  -Lout/build-imx8mp/user_product/lib/rng  -Lout/build-imx8mp/user_product/lib/srv  -Lout/build-imx8mp/user_product/lib/storage  -Lout/build-imx8mp/user_product/lib/syscall-stubs  -Lout/build-imx8mp/user_product/lib/tipc  -lboringssl  -ldlmalloc  -lhwkey  -llibc-ext  -llibc-rctee  -llibcxxabi-trusty  -llibstdc++-trusty  -lline-coverage  -lrng  -lsrv  -lstorage  -lsyscall-stubs  -ltipc
TA_ELF: out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.elf
Parsing Compnent: kernel/rctee/lib/libc-ext
kernel/rctee/lib/libc-ext is lk build form, just be compatible with it!
kernel/rctee/lib/libc-ext variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/libc-ext
LIB_BIN_DEPS: 
LIB_BIN: out/build-imx8mp/user_product/lib/libc-ext/liblibc-ext.a
COMP_OBJS: out/build-imx8mp/user_product/lib/libc-ext/kernel/rctee/lib/libc-ext/scnprintf.o out/build-imx8mp/user_product/lib/libc-ext/kernel/rctee/lib/libc-ext/uuid.o    
Parsing Compnent: opensource_libs/boringssl
opensource_libs/boringssl is lk build form, just be compatible with it!
opensource_libs/boringssl variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/boringssl
LIB_BIN_DEPS:  out/build-imx8mp/user_product/lib/rng/librng.a
LIB_BIN: out/build-imx8mp/user_product/lib/boringssl/libboringssl.a
COMP_OBJS: out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/err_data.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_bitstr.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_bool.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_d2i_fp.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_dup.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_gentm.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_i2d_fp.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_int.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_mbstr.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_object.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_octet.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_strex.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_strnid.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_time.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_type.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/a_utctm.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/asn1_lib.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/asn1_par.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/asn_pack.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/f_int.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/f_string.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/posix_time.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/tasn_dec.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/tasn_enc.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/tasn_fre.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/tasn_new.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/tasn_typ.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/asn1/tasn_utl.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/base64/base64.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bio/bio.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bio/bio_mem.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bio/connect.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bio/errno.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bio/fd.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bio/file.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bio/hexdump.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bio/pair.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bio/printf.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bio/socket.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bio/socket_helper.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/blake2/blake2.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bn_extra/bn_asn1.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bn_extra/convert.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/buf/buf.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bytestring/asn1_compat.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bytestring/ber.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bytestring/cbb.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bytestring/cbs.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bytestring/unicode.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/chacha/chacha.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cipher_extra/cipher_extra.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cipher_extra/derive_key.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_aesctrhmac.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_aesgcmsiv.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_chacha20poly1305.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_des.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_null.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_rc2.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_rc4.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_tls.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cipher_extra/tls_cbc.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/conf/conf.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cpu_aarch64_apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cpu_aarch64_fuchsia.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cpu_aarch64_linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cpu_aarch64_openbsd.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cpu_aarch64_sysreg.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cpu_aarch64_win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cpu_arm_freebsd.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cpu_arm_linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/cpu_intel.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/crypto.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/curve25519/curve25519.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/curve25519/curve25519_64_adx.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/curve25519/spake25519.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/des/des.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/dh_extra/dh_asn1.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/dh_extra/params.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/digest_extra/digest_extra.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/dsa/dsa.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/dsa/dsa_asn1.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/ec_extra/ec_asn1.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/ec_extra/ec_derive.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/ec_extra/hash_to_curve.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/ecdh_extra/ecdh_extra.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/ecdsa_extra/ecdsa_asn1.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/engine/engine.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/err/err.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/evp.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/evp_asn1.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/evp_ctx.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/p_dsa_asn1.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/p_ec.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/p_ec_asn1.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/p_ed25519.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/p_ed25519_asn1.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/p_hkdf.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/p_rsa.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/p_rsa_asn1.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/p_x25519.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/p_x25519_asn1.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/pbkdf.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/print.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/scrypt.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/evp/sign.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/ex_data.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/fipsmodule/bcm.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/fipsmodule/fips_shared_support.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/hpke/hpke.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/hrss/hrss.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/kyber/keccak.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/kyber/kyber.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/lhash/lhash.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/mem.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/obj/obj.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/obj/obj_xref.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pem/pem_all.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pem/pem_info.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pem/pem_lib.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pem/pem_oth.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pem/pem_pk8.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pem/pem_pkey.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pem/pem_x509.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pem/pem_xaux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pkcs7/pkcs7.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pkcs7/pkcs7_x509.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pkcs8/p5_pbev2.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pkcs8/pkcs8.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pkcs8/pkcs8_x509.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/poly1305/poly1305.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/poly1305/poly1305_arm.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/poly1305/poly1305_vec.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/pool/pool.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/rand_extra/deterministic.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/rand_extra/forkunsafe.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/rand_extra/getentropy.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/rand_extra/ios.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/rand_extra/passive.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/rand_extra/rand_extra.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/rand_extra/trusty.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/rand_extra/windows.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/rc4/rc4.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/refcount.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/rsa_extra/rsa_asn1.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/rsa_extra/rsa_crypt.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/rsa_extra/rsa_print.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/siphash/siphash.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/stack/stack.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/thread.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/thread_none.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/thread_pthread.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/thread_win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/trust_token/pmbtoken.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/trust_token/trust_token.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/trust_token/voprf.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/a_digest.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/a_sign.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/a_verify.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/algorithm.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/asn1_gen.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/by_dir.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/by_file.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/i2d_pr.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/name_print.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/policy.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/rsa_pss.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/t_crl.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/t_req.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/t_x509.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/t_x509a.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_att.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_cmp.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_d2.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_def.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_ext.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_lu.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_obj.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_req.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_set.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_trs.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_txt.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_v3.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_vfy.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509_vpm.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509cset.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509name.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509rset.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x509spki.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_algor.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_all.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_attrib.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_crl.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_exten.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_info.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_name.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_pkey.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_pubkey.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_req.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_sig.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_spki.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_val.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_x509.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509/x_x509a.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_akey.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_akeya.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_alt.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_bcons.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_bitst.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_conf.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_cpols.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_crld.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_enum.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_extku.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_genn.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_ia5.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_info.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_int.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_lib.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_ncons.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_ocsp.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_pcons.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_pmaps.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_prn.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_purp.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_skey.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/x509v3/v3_utl.o out/build-imx8mp/user_product/lib/boringssl/kernel/hardware/nxp/user/lib/nxp_openssl_stub/rand.o   out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/chacha/chacha-armv8-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/cipher_extra/chacha20_poly1305_armv8-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/aesv8-armv8-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/aesv8-gcm-armv8-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/armv8-mont-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/bn-armv8-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/ghash-neon-armv8-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/ghashv8-armv8-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/p256-armv8-asm-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/p256_beeu-armv8-asm-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/sha1-armv8-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/sha256-armv8-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/sha512-armv8-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/vpaes-armv8-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-aarch64/crypto/test/trampoline-armv8-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-arm/crypto/chacha/chacha-armv4-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/aesv8-armv7-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/armv4-mont-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/bsaes-armv7-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/ghash-armv4-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/ghashv8-armv7-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/sha1-armv4-large-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/sha256-armv4-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/sha512-armv4-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/vpaes-armv7-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-arm/crypto/test/trampoline-armv4-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86/crypto/chacha/chacha-x86-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/aesni-x86-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/bn-586-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/co-586-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/ghash-ssse3-x86-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/ghash-x86-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/md5-586-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/sha1-586-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/sha256-586-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/sha512-586-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/vpaes-x86-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/x86-mont-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86/crypto/test/trampoline-x86-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/chacha/chacha-x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/cipher_extra/aes128gcmsiv-x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/cipher_extra/chacha20_poly1305_x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/aesni-gcm-x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/aesni-x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/ghash-ssse3-x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/ghash-x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/md5-x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/p256-x86_64-asm-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/p256_beeu-x86_64-asm-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/rdrand-x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/rsaz-avx2-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/sha1-x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/sha256-x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/sha512-x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/vpaes-x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/x86_64-mont-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/x86_64-mont5-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/apple-x86_64/crypto/test/trampoline-x86_64-apple.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/chacha/chacha-armv8-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/cipher_extra/chacha20_poly1305_armv8-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/aesv8-armv8-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/aesv8-gcm-armv8-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/armv8-mont-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/bn-armv8-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/ghash-neon-armv8-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/ghashv8-armv8-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/p256-armv8-asm-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/p256_beeu-armv8-asm-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/sha1-armv8-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/sha256-armv8-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/sha512-armv8-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/vpaes-armv8-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-aarch64/crypto/test/trampoline-armv8-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-arm/crypto/chacha/chacha-armv4-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/aesv8-armv7-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/armv4-mont-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/bsaes-armv7-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/ghash-armv4-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/ghashv8-armv7-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/sha1-armv4-large-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/sha256-armv4-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/sha512-armv4-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/vpaes-armv7-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-arm/crypto/test/trampoline-armv4-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86/crypto/chacha/chacha-x86-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/aesni-x86-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/bn-586-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/co-586-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/ghash-ssse3-x86-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/ghash-x86-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/md5-586-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/sha1-586-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/sha256-586-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/sha512-586-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/vpaes-x86-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/x86-mont-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86/crypto/test/trampoline-x86-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/chacha/chacha-x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/cipher_extra/aes128gcmsiv-x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/cipher_extra/chacha20_poly1305_x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/aesni-gcm-x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/aesni-x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/ghash-ssse3-x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/ghash-x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/md5-x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/p256-x86_64-asm-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/p256_beeu-x86_64-asm-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/rdrand-x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/rsaz-avx2-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/sha1-x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/sha256-x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/sha512-x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/vpaes-x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/x86_64-mont-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/x86_64-mont5-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/linux-x86_64/crypto/test/trampoline-x86_64-linux.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/curve25519/asm/x25519-asm-arm.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/hrss/asm/poly_rq_mul.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/poly1305/poly1305_arm_asm.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/third_party/fiat/asm/fiat_curve25519_adx_mul.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/third_party/fiat/asm/fiat_curve25519_adx_square.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/chacha/chacha-armv8-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/cipher_extra/chacha20_poly1305_armv8-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/aesv8-armv8-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/aesv8-gcm-armv8-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/armv8-mont-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/bn-armv8-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/ghash-neon-armv8-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/ghashv8-armv8-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/p256-armv8-asm-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/p256_beeu-armv8-asm-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/sha1-armv8-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/sha256-armv8-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/sha512-armv8-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/vpaes-armv8-win.o out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/win-aarch64/crypto/test/trampoline-armv8-win.o 
Parsing Compnent: user/base/lib/dlmalloc
user/base/lib/dlmalloc is lk build form, just be compatible with it!
user/base/lib/dlmalloc variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/dlmalloc
LIB_BIN_DEPS: 
LIB_BIN: out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a
COMP_OBJS: out/build-imx8mp/user_product/lib/dlmalloc/user/base/lib/dlmalloc/malloc.o    
Parsing Compnent: user/base/lib/hwaes/srv
user/base/lib/hwaes/srv is lk build form, just be compatible with it!
user/base/lib/hwaes/srv variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/srv
LIB_BIN_DEPS:  out/build-imx8mp/user_product/lib/tipc/libtipc.a
LIB_BIN: out/build-imx8mp/user_product/lib/srv/libsrv.a
COMP_OBJS: out/build-imx8mp/user_product/lib/srv/user/base/lib/hwaes/srv/hwaes_server.o    
Parsing Compnent: user/base/lib/hwkey
user/base/lib/hwkey is lk build form, just be compatible with it!
user/base/lib/hwkey variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/hwkey
LIB_BIN_DEPS: 
LIB_BIN: out/build-imx8mp/user_product/lib/hwkey/libhwkey.a
COMP_OBJS: out/build-imx8mp/user_product/lib/hwkey/user/base/lib/hwkey/hwkey.o    
Parsing Compnent: user/base/lib/libc-rctee
user/base/lib/libc-rctee is lk build form, just be compatible with it!
user/base/lib/libc-rctee variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/libc-rctee
LIB_BIN_DEPS:  out/build-imx8mp/user_product/lib/libc-ext/liblibc-ext.a  out/build-imx8mp/user_product/lib/libc-main/liblibc-main.a  out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a
LIB_BIN: out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a
COMP_OBJS: out/build-imx8mp/user_product/lib/libc-rctee/kernel/lk/lib/libc/eabi_unwind_stubs.o out/build-imx8mp/user_product/lib/libc-rctee/user/base/lib/libc-rctee/__dso_handle.o out/build-imx8mp/user_product/lib/libc-rctee/user/base/lib/libc-rctee/__set_thread_area.o out/build-imx8mp/user_product/lib/libc-rctee/user/base/lib/libc-rctee/file_stubs.o out/build-imx8mp/user_product/lib/libc-rctee/user/base/lib/libc-rctee/locale_stubs.o out/build-imx8mp/user_product/lib/libc-rctee/user/base/lib/libc-rctee/time_stubs.o out/build-imx8mp/user_product/lib/libc-rctee/user/base/lib/libc-rctee/ipc.o out/build-imx8mp/user_product/lib/libc-rctee/user/base/lib/libc-rctee/logging.o out/build-imx8mp/user_product/lib/libc-rctee/user/base/lib/libc-rctee/memref.o out/build-imx8mp/user_product/lib/libc-rctee/user/base/lib/libc-rctee/mman.o out/build-imx8mp/user_product/lib/libc-rctee/user/base/lib/libc-rctee/time.o out/build-imx8mp/user_product/lib/libc-rctee/user/base/lib/libc-rctee/rctee_err.o out/build-imx8mp/user_product/lib/libc-rctee/user/base/lib/libc-rctee/rctee_uio.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/env/__environ.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/env/__init_tls.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/env/__libc_start_main.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/env/__stack_chk_fail.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/env/getenv.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/internal/defsysinfo.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/internal/floatscan.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/internal/intscan.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/internal/libc.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/internal/shgetc.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/__ctype_b_loc.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/__ctype_get_mb_cur_max.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/__ctype_tolower_loc.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/__ctype_toupper_loc.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/isalnum.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/isalpha.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/isascii.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/isblank.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iscntrl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/isdigit.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/isgraph.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/islower.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/isprint.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/ispunct.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/isspace.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/isupper.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iswalnum.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iswalpha.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iswblank.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iswcntrl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iswctype.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iswdigit.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iswgraph.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iswlower.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iswprint.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iswpunct.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iswspace.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iswupper.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/iswxdigit.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/isxdigit.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/toascii.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/tolower.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/toupper.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/towctrans.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/wcswidth.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/wctrans.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/ctype/wcwidth.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/errno/strerror.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/errno/__errno_location.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/exit/abort.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/exit/assert.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/exit/atexit.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/exit/exit.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/exit/_Exit.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/misc/getauxval.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/btowc.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/c16rtomb.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/c32rtomb.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/internal.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/mblen.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/mbrlen.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/mbrtoc16.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/mbrtoc32.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/mbrtowc.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/mbsinit.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/mbsnrtowcs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/mbsrtowcs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/mbstowcs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/mbtowc.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/wcrtomb.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/wcsnrtombs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/wcsrtombs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/wcstombs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/wctob.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/multibyte/wctomb.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/network/htonl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/network/htons.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/network/ntohl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/network/ntohs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/prng/rand.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/abs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/atof.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/atoi.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/atol.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/atoll.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/bsearch.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/div.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/ecvt.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/fcvt.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/gcvt.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/imaxabs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/imaxdiv.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/labs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/ldiv.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/llabs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/lldiv.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/qsort.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/strtod.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/strtol.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/wcstod.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdlib/wcstol.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/bcmp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/memccpy.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/memchr.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/memcmp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/memcpy.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/memmem.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/memmove.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/mempcpy.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/memrchr.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/memset.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/stpcpy.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/stpncpy.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strcasecmp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strcasestr.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strcat.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strchr.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strchrnul.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strcmp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strcpy.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strcspn.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strdup.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strerror_r.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strlen.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strncasecmp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strncat.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strncmp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strncpy.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strndup.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strnlen.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strpbrk.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strrchr.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strsep.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strsignal.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strspn.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strstr.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strtok.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strtok_r.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/strverscmp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/swab.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcpcpy.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcpncpy.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcscasecmp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcscasecmp_l.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcscat.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcschr.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcscmp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcscpy.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcscspn.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcsdup.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcslen.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcsncasecmp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcsncasecmp_l.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcsncat.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcsncmp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcsncpy.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcsnlen.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcspbrk.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcsrchr.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcsspn.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcsstr.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcstok.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wcswcs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wmemchr.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wmemcmp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wmemcpy.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wmemmove.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/string/wmemset.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/asprintf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/fclose.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/fflush.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/fileno.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/fputc.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/fputs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/fprintf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/fread.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/fseek.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/ftell.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/fwrite.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/getc.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/ofl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/printf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/putc_unlocked.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/putchar.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/puts.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/sscanf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/snprintf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/sprintf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/stderr.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/stdin.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/stdout.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/ungetc.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/vasprintf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/vprintf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/vfprintf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/vsnprintf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/vsprintf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/vfscanf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/vsscanf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/__lockfile.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/__overflow.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/__stdio_close.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/__stdio_exit.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/__stdio_read.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/__stdio_write.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/__stdio_seek.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/__string_read.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/__toread.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/__towrite.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/stdio/__uflow.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/thread/__lock.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/thread/__wait.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/thread/default_attr.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/thread/pthread_once.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/thread/pthread_cleanup_push.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/time/gettimeofday.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/time/localtime.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/time/localtime_r.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/time/gmtime.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/time/gmtime_r.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/time/time.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/time/__secs_to_tm.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/unistd/sleep.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/unistd/usleep.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/acos.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/acosf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/acosh.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/acoshf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/acoshl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/acosl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/asin.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/asinf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/asinh.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/asinhf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/asinhl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/asinl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/atan2.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/atan2f.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/atan2l.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/atan.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/atanf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/atanh.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/atanhf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/atanhl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/atanl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/cbrt.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/cbrtf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/cbrtl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/ceil.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/ceilf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/ceill.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/copysign.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/copysignf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/copysignl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__cos.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/cos.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__cosdf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/cosf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/cosh.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/coshf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/coshl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__cosl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/cosl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/erf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/erff.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/erfl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/exp10.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/exp10f.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/exp10l.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/exp2.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/exp2f.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/exp2f_data.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/exp2l.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/exp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/exp_data.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/expf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/expl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/expm1.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/expm1f.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/expm1l.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__expo2.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__expo2f.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fabs.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fabsf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fabsl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fdim.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fdimf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fdiml.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/finite.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/finitef.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/floor.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/floorf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/floorl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fma.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fmaf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fmal.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fmax.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fmaxf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fmaxl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fmin.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fminf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fminl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fmod.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fmodf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/fmodl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__fpclassify.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__fpclassifyf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__fpclassifyl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/frexp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/frexpf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/frexpl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/hypot.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/hypotf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/hypotl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/ilogb.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/ilogbf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/ilogbl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__invtrigl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/j0.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/j0f.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/j1.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/j1f.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/jn.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/jnf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/ldexp.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/ldexpf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/ldexpl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/lgamma.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/lgammaf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/lgammaf_r.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/lgammal.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/lgamma_r.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/llrint.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/llrintf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/llrintl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/llround.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/llroundf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/llroundl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/log10.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/log10f.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/log10l.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/log1p.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/log1pf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/log1pl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/log2.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/log2_data.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/log2f.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/log2f_data.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/log2l.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/logb.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/logbf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/logbl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/log.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/log_data.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/logf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/logf_data.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/logl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/lrint.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/lrintf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/lrintl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/lround.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/lroundf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/lroundl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__math_divzero.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__math_divzerof.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__math_invalid.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__math_invalidf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__math_oflow.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__math_oflowf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__math_uflow.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__math_uflowf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__math_xflow.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__math_xflowf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/modf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/modff.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/modfl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/nan.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/nanf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/nanl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/nearbyint.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/nearbyintf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/nearbyintl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/nextafter.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/nextafterf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/nextafterl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/nexttoward.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/nexttowardf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/nexttowardl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__polevll.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/pow.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/pow_data.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/powf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/powf_data.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/powl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/remainder.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/remainderf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/remainderl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__rem_pio2.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__rem_pio2f.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__rem_pio2_large.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__rem_pio2l.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/remquo.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/remquof.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/remquol.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/rint.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/rintf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/rintl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/round.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/roundf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/roundl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/scalb.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/scalbf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/scalbln.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/scalblnf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/scalblnl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/scalbn.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/scalbnf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/scalbnl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__signbit.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__signbitf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__signbitl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/signgam.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/significand.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/significandf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__sin.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/sin.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/sincos.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/sincosf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/sincosl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__sindf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/sinf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/sinh.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/sinhf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/sinhl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__sinl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/sinl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/sqrt.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/sqrtf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/sqrtl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__tan.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/tan.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__tandf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/tanf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/tanh.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/tanhf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/tanhl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/__tanl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/tanl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/tgamma.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/tgammaf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/tgammal.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/trunc.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/truncf.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/math/truncl.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/bind_textdomain_codeset.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/catclose.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/catgets.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/catopen.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/c_locale.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/iconv.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/iconv_close.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/langinfo.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/__lctrans.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/localeconv.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/__mo_lookup.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/pleval.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/strcoll.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/strfmon.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/strxfrm.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/textdomain.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/wcscoll.o out/build-imx8mp/user_product/lib/libc-rctee/opensource_libs/musl/src/locale/wcsxfrm.o out/build-imx8mp/user_product/lib/libc-rctee/user/base/lib/libc-rctee/pthreads.o    
Parsing Compnent: user/base/lib/libc-main
user/base/lib/libc-main is lk build form, just be compatible with it!
user/base/lib/libc-main variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/libc-main
LIB_BIN_DEPS: 
LIB_BIN: out/build-imx8mp/user_product/lib/libc-main/liblibc-main.a
COMP_OBJS: out/build-imx8mp/user_product/lib/libc-main/opensource_libs/musl/crt/rcrt1.o    
Parsing Compnent: user/base/lib/libcxxabi-trusty
user/base/lib/libcxxabi-trusty is lk build form, just be compatible with it!
user/base/lib/libcxxabi-trusty variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/libcxxabi-trusty
LIB_BIN_DEPS: 
LIB_BIN: out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a
COMP_OBJS:  out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/cxa_aux_runtime.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/cxa_default_handlers.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/cxa_demangle.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/cxa_exception_storage.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/cxa_guard.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/cxa_handlers.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/cxa_unexpected.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/cxa_vector.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/cxa_virtual.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/stdlib_exception.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/stdlib_stdexcept.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/stdlib_typeinfo.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/abort_message.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/fallback_malloc.o out/build-imx8mp/user_product/lib/libcxxabi-trusty/opensource_libs/libcxxabi/src/cxa_noexception.o   
Parsing Compnent: user/base/lib/libstdc++-trusty
user/base/lib/libstdc++-trusty is lk build form, just be compatible with it!
user/base/lib/libstdc++-trusty variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/libstdc++-trusty
LIB_BIN_DEPS:  out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a  out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a
LIB_BIN: out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a
COMP_OBJS:  out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/algorithm.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/any.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/bind.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/charconv.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/chrono.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/condition_variable.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/debug.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/exception.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/future.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/hash.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/ios.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/iostream.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/locale.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/memory.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/mutex.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/new.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/optional.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/regex.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/shared_mutex.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/string.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/strstream.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/system_error.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/thread.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/typeinfo.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/utility.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/valarray.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/variant.o out/build-imx8mp/user_product/lib/libstdc++-trusty/opensource_libs/libcxx/src/vector.o   
Parsing Compnent: user/base/lib/line-coverage
user/base/lib/line-coverage is lk build form, just be compatible with it!
user/base/lib/line-coverage variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/line-coverage
LIB_BIN_DEPS:  out/build-imx8mp/user_product/lib/common/libcommon.a  out/build-imx8mp/user_product/lib/tipc/libtipc.a
LIB_BIN: out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a
COMP_OBJS: out/build-imx8mp/user_product/lib/line-coverage/user/base/lib/line-coverage/shm.o    
Parsing Compnent: user/base/lib/coverage/common
user/base/lib/coverage/common is lk build form, just be compatible with it!
user/base/lib/coverage/common variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/common
LIB_BIN_DEPS:  out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a  out/build-imx8mp/user_product/lib/tipc/libtipc.a
LIB_BIN: out/build-imx8mp/user_product/lib/common/libcommon.a
COMP_OBJS: out/build-imx8mp/user_product/lib/common/user/base/lib/coverage/common/ipc.o out/build-imx8mp/user_product/lib/common/user/base/lib/coverage/common/cov_shm.o    
Parsing Compnent: user/base/lib/rng
user/base/lib/rng is lk build form, just be compatible with it!
user/base/lib/rng variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/rng
LIB_BIN_DEPS: 
LIB_BIN: out/build-imx8mp/user_product/lib/rng/librng.a
COMP_OBJS: out/build-imx8mp/user_product/lib/rng/user/base/lib/rng/trusty_rng.o    
Parsing Compnent: user/base/lib/storage
user/base/lib/storage is lk build form, just be compatible with it!
user/base/lib/storage variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/storage
LIB_BIN_DEPS: 
LIB_BIN: out/build-imx8mp/user_product/lib/storage/libstorage.a
COMP_OBJS: out/build-imx8mp/user_product/lib/storage/user/base/lib/storage/storage.o    
Parsing Compnent: user/base/lib/syscall-stubs
user/base/lib/syscall-stubs is lk build form, just be compatible with it!
user/base/lib/syscall-stubs variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/syscall-stubs
LIB_BIN_DEPS: 
LIB_BIN: out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a
COMP_OBJS:    out/build-imx8mp/user_product/lib/syscall-stubs/out/build-imx8mp/generated/user/base/lib/syscall-stubs/rctee_syscalls.o 
Parsing Compnent: user/base/lib/tipc
user/base/lib/tipc is lk build form, just be compatible with it!
user/base/lib/tipc variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/tipc
LIB_BIN_DEPS: 
LIB_BIN: out/build-imx8mp/user_product/lib/tipc/libtipc.a
COMP_OBJS: out/build-imx8mp/user_product/lib/tipc/user/base/lib/tipc/tipc.o out/build-imx8mp/user_product/lib/tipc/user/base/lib/tipc/tipc_srv.o    
Parsing Compnent: user/app/sample/manifest-test
user/app/sample/manifest-test is lk build form, just be compatible with it!
generating manifest for user/app/sample/manifest-test: out/build-imx8mp/user_product/ta/manifest-test/manifest-test.manifest
user/app/sample/manifest-test variables will all be reset!
TA_COMP_OUTDIR: out/build-imx8mp/user_product/ta/manifest-test
TA_LDFLAGS:  -z max-page-size=4096 -z separate-loadable-segments --undefined=__aeabi_unwind_cpp_pr0 --gc-sections -static -pie --no-dynamic-linker -z text -Bsymbolic  -Lout/build-imx8mp/user_product/lib/apploader_package  -Lout/build-imx8mp/user_product/lib/dlmalloc  -Lout/build-imx8mp/user_product/lib/libc-rctee  -Lout/build-imx8mp/user_product/lib/libcxxabi-trusty  -Lout/build-imx8mp/user_product/lib/libstdc++-trusty  -Lout/build-imx8mp/user_product/lib/line-coverage  -Lout/build-imx8mp/user_product/lib/rng  -Lout/build-imx8mp/user_product/lib/syscall-stubs  -Lout/build-imx8mp/user_product/lib/tipc  -Lout/build-imx8mp/user_product/lib/unittest  -lapploader_package  -ldlmalloc  -llibc-rctee  -llibcxxabi-trusty  -llibstdc++-trusty  -lline-coverage  -lrng  -lsyscall-stubs  -ltipc  -lunittest
TA_ELF: out/build-imx8mp/user_product/ta/manifest-test/manifest-test.elf
Parsing Compnent: user/base/lib/apploader_package
user/base/lib/apploader_package is lk build form, just be compatible with it!
user/base/lib/apploader_package variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/apploader_package
LIB_BIN_DEPS:  out/build-imx8mp/user_product/lib/boringssl/libboringssl.a  out/build-imx8mp/user_product/lib/open-dice/libopen-dice.a  out/build-imx8mp/user_product/lib/hwaes/libhwaes.a  out/build-imx8mp/user_product/lib/hwkey/libhwkey.a
LIB_BIN: out/build-imx8mp/user_product/lib/apploader_package/libapploader_package.a
COMP_OBJS:  out/build-imx8mp/user_product/lib/apploader_package/user/base/lib/apploader_package/cose.o out/build-imx8mp/user_product/lib/apploader_package/user/base/lib/apploader_package/package.o   
Parsing Compnent: opensource_libs/open-dice
opensource_libs/open-dice is lk build form, just be compatible with it!
opensource_libs/open-dice variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/open-dice
LIB_BIN_DEPS:  out/build-imx8mp/user_product/lib/boringssl/libboringssl.a
LIB_BIN: out/build-imx8mp/user_product/lib/open-dice/libopen-dice.a
COMP_OBJS: out/build-imx8mp/user_product/lib/open-dice/opensource_libs/open-dice/src/android.o out/build-imx8mp/user_product/lib/open-dice/opensource_libs/open-dice/src/boringssl_hash_kdf_ops.o out/build-imx8mp/user_product/lib/open-dice/opensource_libs/open-dice/src/boringssl_ed25519_ops.o out/build-imx8mp/user_product/lib/open-dice/opensource_libs/open-dice/src/cbor_cert_op.o out/build-imx8mp/user_product/lib/open-dice/opensource_libs/open-dice/src/cbor_ed25519_cert_op.o out/build-imx8mp/user_product/lib/open-dice/opensource_libs/open-dice/src/cbor_reader.o out/build-imx8mp/user_product/lib/open-dice/opensource_libs/open-dice/src/cbor_writer.o out/build-imx8mp/user_product/lib/open-dice/opensource_libs/open-dice/src/clear_memory.o out/build-imx8mp/user_product/lib/open-dice/opensource_libs/open-dice/src/dice.o out/build-imx8mp/user_product/lib/open-dice/opensource_libs/open-dice/src/utils.o    
Parsing Compnent: user/base/lib/hwaes
user/base/lib/hwaes is lk build form, just be compatible with it!
user/base/lib/hwaes variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/hwaes
LIB_BIN_DEPS:  out/build-imx8mp/user_product/lib/tipc/libtipc.a
LIB_BIN: out/build-imx8mp/user_product/lib/hwaes/libhwaes.a
COMP_OBJS: out/build-imx8mp/user_product/lib/hwaes/user/base/lib/hwaes/hwaes.o    
Parsing Compnent: user/base/lib/unittest
user/base/lib/unittest is lk build form, just be compatible with it!
user/base/lib/unittest variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/unittest
LIB_BIN_DEPS:  out/build-imx8mp/user_product/lib/common/libcommon.a  out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a  out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a  out/build-imx8mp/user_product/lib/tipc/libtipc.a
LIB_BIN: out/build-imx8mp/user_product/lib/unittest/libunittest.a
COMP_OBJS: out/build-imx8mp/user_product/lib/unittest/user/base/lib/unittest/unittest.o    
Parsing Compnent: user/app/sample/memref-test/receiver
user/app/sample/memref-test/receiver is lk build form, just be compatible with it!
generating manifest for user/app/sample/memref-test/receiver: out/build-imx8mp/user_product/ta/receiver/receiver.manifest
user/app/sample/memref-test/receiver variables will all be reset!
TA_COMP_OUTDIR: out/build-imx8mp/user_product/ta/receiver
TA_LDFLAGS:  -z max-page-size=4096 -z separate-loadable-segments --undefined=__aeabi_unwind_cpp_pr0 --gc-sections -static -pie --no-dynamic-linker -z text -Bsymbolic  -Lout/build-imx8mp/user_product/lib/dlmalloc  -Lout/build-imx8mp/user_product/lib/libc-rctee  -Lout/build-imx8mp/user_product/lib/libcxxabi-trusty  -Lout/build-imx8mp/user_product/lib/libstdc++-trusty  -Lout/build-imx8mp/user_product/lib/line-coverage  -Lout/build-imx8mp/user_product/lib/rng  -Lout/build-imx8mp/user_product/lib/syscall-stubs  -Lout/build-imx8mp/user_product/lib/tipc  -Lout/build-imx8mp/user_product/lib/unittest  -ldlmalloc  -llibc-rctee  -llibcxxabi-trusty  -llibstdc++-trusty  -lline-coverage  -lrng  -lsyscall-stubs  -ltipc  -lunittest
TA_ELF: out/build-imx8mp/user_product/ta/receiver/receiver.elf
Parsing Compnent: user/app/test/tongsuo_test
user/app/test/tongsuo_test is lk build form, just be compatible with it!
generating manifest for user/app/test/tongsuo_test: out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.manifest
user/app/test/tongsuo_test variables will all be reset!
TA_COMP_OUTDIR: out/build-imx8mp/user_product/ta/tongsuo_test
TA_LDFLAGS:  -z max-page-size=4096 -z separate-loadable-segments --undefined=__aeabi_unwind_cpp_pr0 --gc-sections -static -pie --no-dynamic-linker -z text -Bsymbolic  -Lout/build-imx8mp/user_product/lib/Tongsuo  -Lout/build-imx8mp/user_product/lib/dlmalloc  -Lout/build-imx8mp/user_product/lib/libc-rctee  -Lout/build-imx8mp/user_product/lib/libcxxabi-trusty  -Lout/build-imx8mp/user_product/lib/libstdc++-trusty  -Lout/build-imx8mp/user_product/lib/line-coverage  -Lout/build-imx8mp/user_product/lib/rng  -Lout/build-imx8mp/user_product/lib/syscall-stubs  -Lout/build-imx8mp/user_product/lib/tipc  -lTongsuo  -ldlmalloc  -llibc-rctee  -llibcxxabi-trusty  -llibstdc++-trusty  -lline-coverage  -lrng  -lsyscall-stubs  -ltipc
TA_ELF: out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.elf
====== Tongsuo Build (Final) ======
Parsing Compnent: opensource_libs/Tongsuo
opensource_libs/Tongsuo is lk build form, just be compatible with it!
opensource_libs/Tongsuo variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/Tongsuo
LIB_BIN_DEPS: 
LIB_BIN: out/build-imx8mp/user_product/lib/Tongsuo/libTongsuo.a
COMP_OBJS: out/build-imx8mp/user_product/lib/Tongsuo/opensource_libs/Tongsuo/crypto/sm2/sm2_crypt.o out/build-imx8mp/user_product/lib/Tongsuo/opensource_libs/Tongsuo/crypto/sm2/sm2_sign.o out/build-imx8mp/user_product/lib/Tongsuo/opensource_libs/Tongsuo/crypto/sm3/sm3.o out/build-imx8mp/user_product/lib/Tongsuo/opensource_libs/Tongsuo/crypto/sm4/sm4.o out/build-imx8mp/user_product/lib/Tongsuo/opensource_libs/Tongsuo/crypto/cryptlib.o out/build-imx8mp/user_product/lib/Tongsuo/opensource_libs/Tongsuo/crypto/mem.o out/build-imx8mp/user_product/lib/Tongsuo/opensource_libs/Tongsuo/crypto/err/err.o    
Parsing Compnent: user/base/app/apploader
user/base/app/apploader is lk build form, just be compatible with it!
generating manifest for user/base/app/apploader: out/build-imx8mp/user_product/ta/apploader/apploader.manifest
user/base/app/apploader variables will all be reset!
TA_COMP_OUTDIR: out/build-imx8mp/user_product/ta/apploader
TA_LDFLAGS:  -z max-page-size=4096 -z separate-loadable-segments --undefined=__aeabi_unwind_cpp_pr0 --gc-sections -static -pie --no-dynamic-linker -z text -Bsymbolic  -Lout/build-imx8mp/user_product/lib/app_manifest  -Lout/build-imx8mp/user_product/lib/apploader_package  -Lout/build-imx8mp/user_product/lib/apploader_policy_engine  -Lout/build-imx8mp/user_product/lib/boringssl  -Lout/build-imx8mp/user_product/lib/dlmalloc  -Lout/build-imx8mp/user_product/lib/hwaes  -Lout/build-imx8mp/user_product/lib/hwkey  -Lout/build-imx8mp/user_product/lib/libc-rctee  -Lout/build-imx8mp/user_product/lib/libcxxabi-trusty  -Lout/build-imx8mp/user_product/lib/libstdc++-trusty  -Lout/build-imx8mp/user_product/lib/line-coverage  -Lout/build-imx8mp/user_product/lib/open-dice  -Lout/build-imx8mp/user_product/lib/rng  -Lout/build-imx8mp/user_product/lib/storage  -Lout/build-imx8mp/user_product/lib/syscall-stubs  -Lout/build-imx8mp/user_product/lib/system_state  -Lout/build-imx8mp/user_product/lib/tipc  -lapp_manifest  -lapploader_package  -lapploader_policy_engine  -lboringssl  -ldlmalloc  -lhwaes  -lhwkey  -llibc-rctee  -llibcxxabi-trusty  -llibstdc++-trusty  -lline-coverage  -lopen-dice  -lrng  -lstorage  -lsyscall-stubs  -lsystem_state  -ltipc
TA_ELF: out/build-imx8mp/user_product/ta/apploader/apploader.elf
Parsing Compnent: kernel/rctee/lib/app_manifest
kernel/rctee/lib/app_manifest is lk build form, just be compatible with it!
kernel/rctee/lib/app_manifest variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/app_manifest
LIB_BIN_DEPS: 
LIB_BIN: out/build-imx8mp/user_product/lib/app_manifest/libapp_manifest.a
COMP_OBJS: out/build-imx8mp/user_product/lib/app_manifest/kernel/rctee/lib/app_manifest/app_manifest.o    
Parsing Compnent: user/base/lib/sample/apploader_policy_engine
user/base/lib/sample/apploader_policy_engine is lk build form, just be compatible with it!
user/base/lib/sample/apploader_policy_engine variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/apploader_policy_engine
LIB_BIN_DEPS:  out/build-imx8mp/user_product/lib/hwkey/libhwkey.a  out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a
LIB_BIN: out/build-imx8mp/user_product/lib/apploader_policy_engine/libapploader_policy_engine.a
COMP_OBJS:  out/build-imx8mp/user_product/lib/apploader_policy_engine/user/base/lib/sample/apploader_policy_engine/apploader_policy_engine.o   
Parsing Compnent: user/base/lib/system_state
user/base/lib/system_state is lk build form, just be compatible with it!
user/base/lib/system_state variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/system_state
LIB_BIN_DEPS:  out/build-imx8mp/user_product/lib/tipc/libtipc.a
LIB_BIN: out/build-imx8mp/user_product/lib/system_state/libsystem_state.a
COMP_OBJS: out/build-imx8mp/user_product/lib/system_state/user/base/lib/system_state/system_state.o    
Parsing Compnent: user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_1
user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_1 is lk build form, just be compatible with it!
generating manifest for user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_1: out/build-imx8mp/user_product/ta/dynamic_ta_test_1/dynamic_ta_test_1.manifest
user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_1 variables will all be reset!
TA_COMP_OUTDIR: out/build-imx8mp/user_product/ta/dynamic_ta_test_1
TA_LDFLAGS:  -z max-page-size=4096 -z separate-loadable-segments --undefined=__aeabi_unwind_cpp_pr0 --gc-sections -static -pie --no-dynamic-linker -z text -Bsymbolic  -Lout/build-imx8mp/user_product/lib/dlmalloc  -Lout/build-imx8mp/user_product/lib/libc-ext  -Lout/build-imx8mp/user_product/lib/libc-rctee  -Lout/build-imx8mp/user_product/lib/libcxxabi-trusty  -Lout/build-imx8mp/user_product/lib/libstdc++-trusty  -Lout/build-imx8mp/user_product/lib/libutee  -Lout/build-imx8mp/user_product/lib/line-coverage  -Lout/build-imx8mp/user_product/lib/rng  -Lout/build-imx8mp/user_product/lib/syscall-stubs  -Lout/build-imx8mp/user_product/lib/tipc  -ldlmalloc  -llibc-ext  -llibc-rctee  -llibcxxabi-trusty  -llibstdc++-trusty  -llibutee  -lline-coverage  -lrng  -lsyscall-stubs  -ltipc
TA_ELF: out/build-imx8mp/user_product/ta/dynamic_ta_test_1/dynamic_ta_test_1.elf
Parsing Compnent: user/base/lib/libutee
user/base/lib/libutee is lk build form, just be compatible with it!
user/base/lib/libutee variables will all be reset!
LIB_COMP_OUTDIR: out/build-imx8mp/user_product/lib/libutee
LIB_BIN_DEPS:  out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a  out/build-imx8mp/user_product/lib/tipc/libtipc.a
LIB_BIN: out/build-imx8mp/user_product/lib/libutee/liblibutee.a
COMP_OBJS: out/build-imx8mp/user_product/lib/libutee/user/base/lib/libutee/user_header.o out/build-imx8mp/user_product/lib/libutee/user/base/lib/libutee/tee_api_property.o    
TOTAL_USER_TARGETS:  out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.signed out/build-imx8mp/user_product/lib/libc-ext/liblibc-ext.a out/build-imx8mp/user_product/lib/boringssl/libboringssl.a out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a out/build-imx8mp/user_product/lib/srv/libsrv.a out/build-imx8mp/user_product/lib/hwkey/libhwkey.a out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a out/build-imx8mp/user_product/lib/libc-main/liblibc-main.a out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a out/build-imx8mp/user_product/lib/common/libcommon.a out/build-imx8mp/user_product/lib/rng/librng.a out/build-imx8mp/user_product/lib/storage/libstorage.a out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a out/build-imx8mp/user_product/lib/tipc/libtipc.a out/build-imx8mp/user_product/ta/manifest-test/manifest-test.signed out/build-imx8mp/user_product/lib/apploader_package/libapploader_package.a out/build-imx8mp/user_product/lib/open-dice/libopen-dice.a out/build-imx8mp/user_product/lib/hwaes/libhwaes.a out/build-imx8mp/user_product/lib/unittest/libunittest.a out/build-imx8mp/user_product/ta/receiver/receiver.signed out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.signed out/build-imx8mp/user_product/lib/Tongsuo/libTongsuo.a out/build-imx8mp/user_product/ta/apploader/apploader.signed out/build-imx8mp/user_product/lib/app_manifest/libapp_manifest.a out/build-imx8mp/user_product/lib/apploader_policy_engine/libapploader_policy_engine.a out/build-imx8mp/user_product/lib/system_state/libsystem_state.a out/build-imx8mp/user_product/ta/dynamic_ta_test_1/dynamic_ta_test_1.signed out/build-imx8mp/user_product/lib/libutee/liblibutee.a
EXPORTED_OPTFLAGS: 
EXPORTED_COMPILEFLAGS: -DIMX8MP_LCDIF_INDEX=1 -D_ALL_SOURCE -fdata-sections -ffixed-x18 -ffunction-sections -target aarch64-linux-gnu
EXPORTED_CFLAGS: --std=c17 -Wstrict-prototypes -Wwrite-strings
EXPORTED_CPPFLAGS: --std=c++17 -D_LIBCPP_BUILD_STATIC -D_LIBCPP_HAS_C11_FEATURES -D_LIBCPP_HAS_MUSL_LIBC -D_LIBCPP_HAS_QUICK_EXIT -D_LIBCPP_HAS_THREAD_API_PTHREAD -D_LIBCPP_HAS_TIMESPEC_GET -fno-exceptions -fno-rtti -fno-threadsafe-statics
EXPORTED_INCLUDES: -Ikernel/hardware/nxp/platform/imx/common/include -Ikernel/lk/arch/arm64/include -Ikernel/lk/include/shared -Ikernel/lk/include/uapi -Ikernel/lk/lib/binary_search_tree/include -Ikernel/rctee/include/shared -Ikernel/rctee/include/uapi -Ikernel/rctee/lib/app_manifest/include -Ikernel/rctee/lib/libc-ext/include -Iopensource_libs/boringssl/src/include -Iopensource_libs/libcxx/include -Iopensource_libs/libcxxabi/include -Iopensource_libs/musl/arch/aarch64 -Iopensource_libs/musl/arch/generic -Iopensource_libs/musl/include -Iopensource_libs/open-dice/include/ -Iopensource_libs/open-dice/include/dice/config/boringssl_ed25519 -Iout/build-imx8mp/generated/user/base/lib/syscall-stubs -Iuser/base/include/user -Iuser/base/interface/apploader/include -Iuser/base/interface/coverage/include -Iuser/base/interface/hwaes/include/ -Iuser/base/interface/hwcrypto/include/ -Iuser/base/interface/hwkey/include -Iuser/base/interface/hwrng/include -Iuser/base/interface/line-coverage/include -Iuser/base/interface/storage/include -Iuser/base/interface/system_state/include -Iuser/base/lib/apploader_package/include -Iuser/base/lib/apploader_policy_engine/include -Iuser/base/lib/coverage/common/include -Iuser/base/lib/hwaes/include/ -Iuser/base/lib/hwaes/srv/include/ -Iuser/base/lib/hwkey/include -Iuser/base/lib/libc-rctee/include -Iuser/base/lib/libstdc++-trusty/include -Iuser/base/lib/libutee/include -Iuser/base/lib/line-coverage/include -Iuser/base/lib/rng/include -Iuser/base/lib/sample/apploader_policy_engine/include -Iuser/base/lib/storage/include -Iuser/base/lib/system_state/include -Iuser/base/lib/tipc/include -Iuser/base/lib/unittest/include/
EXPORTED_ASMFLAGS: -DASSEMBLY
TOTAL_LIBS: kernel/rctee/lib/app_manifest kernel/rctee/lib/libc-ext opensource_libs/Tongsuo opensource_libs/boringssl opensource_libs/open-dice user/base/lib/apploader_package user/base/lib/coverage/common user/base/lib/dlmalloc user/base/lib/hwaes user/base/lib/hwaes/srv user/base/lib/hwkey user/base/lib/libc-main user/base/lib/libc-rctee user/base/lib/libcxxabi-trusty user/base/lib/libstdc++-trusty user/base/lib/libutee user/base/lib/line-coverage user/base/lib/rng user/base/lib/sample/apploader_policy_engine user/base/lib/storage user/base/lib/syscall-stubs user/base/lib/system_state user/base/lib/tipc user/base/lib/unittest 
BUNDLED_TA_TASKS: user/base/app/apploader kernel/hardware/nxp/app/hwcrypto  user/app/sample/memref-test/receiver user/app/sample/manifest-test user/app/test/tongsuo_test 
BUNDLED_TA_TASK_ELFS:  out/build-imx8mp/user_product/ta/apploader/apploader.elf  out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.elf  out/build-imx8mp/user_product/ta/receiver/receiver.elf  out/build-imx8mp/user_product/ta/manifest-test/manifest-test.elf  out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.elf
LIBGCC = /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/../runtimes_ndk_cxx/libclang_rt.builtins-aarch64-android.a
GLOBAL_COMPILEFLAGS = -glldb -fdebug-macro -include out/build-imx8mp/config.h -Werror -Wall -Wsign-compare -Wno-multichar -Wno-unused-function -Wno-unused-label -fno-short-enums -fno-common -fno-omit-frame-pointer -Wimplicit-fallthrough -Wvla -ffunction-sections -fdata-sections -U__linux__ --sysroot=fake_sysroot -finline -isystem opensource_libs/musl/arch/aarch64 -isystem opensource_libs/musl/arch/generic -isystem opensource_libs/musl/include  -D_ALL_SOURCE -fPIE -fvisibility=hidden
GLOBAL_OPTFLAGS = -O2
generating out/build-imx8mp/toolchain.config
generating out/build-imx8mp/kernel/lk/arch/arm64/module_config.h
generating out/build-imx8mp/config.h
generating out/build-imx8mp/kernel/lk/app/module_config.h
generating out/build-imx8mp/kernel/lk/dev/module_config.h
generating out/build-imx8mp/kernel/hardware/nxp/platform/imx/module_config.h
generating out/build-imx8mp/kernel/lk/dev/interrupt/arm_gic/module_config.h
generating out/build-imx8mp/kernel/lk/dev/timer/arm_generic/module_config.h
generating out/build-imx8mp/kernel/lk/kernel/module_config.h
generating out/build-imx8mp/kernel/lk/kernel/vm/module_config.h
generating out/build-imx8mp/kernel/lk/lib/binary_search_tree/module_config.h
generating out/build-imx8mp/kernel/lk/lib/cbuf/module_config.h
generating out/build-imx8mp/kernel/lk/lib/debug/module_config.h
generating out/build-imx8mp/kernel/lk/lib/heap/module_config.h
generating out/build-imx8mp/kernel/lk/lib/fixed_point/module_config.h
generating out/build-imx8mp/kernel/lk/lib/io/module_config.h
generating out/build-imx8mp/kernel/lk/lib/heap/miniheap/module_config.h
generating out/build-imx8mp/kernel/lk/platform/module_config.h
generating out/build-imx8mp/kernel/lk/target/module_config.h
generating out/build-imx8mp/kernel/rctee/app/busytest/module_config.h
generating out/build-imx8mp/kernel/lk/top/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/backtrace/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/app_manifest/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/arm_ffa/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/ktipc/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/extmem/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/ktipc/test/main/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/ktipc/test/srv/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/libc-ext/module_config.h
make[2]: Circular out/build-imx8mp/kernel/rctee/lib/version/version.o <- out/build-imx8mp/kernel/rctee/lib/version.mod.a dependency dropped.
generating out/build-imx8mp/kernel/rctee/lib/memlog/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/libc-trusty/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/sm/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/rand/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/rctee/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/smc/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/syscall/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/unittest/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/ubsan/module_config.h
generating out/build-imx8mp/kernel/rctee/lib/version/module_config.h
generating out/build-imx8mp/kernel/rctee/services/apploader/module_config.h
generating out/build-imx8mp/kernel/rctee/services/generic_ta_service/module_config.h
generating out/build-imx8mp/kernel/rctee/services/hwrng/module_config.h
generating out/build-imx8mp/kernel/rctee/services/smc/module_config.h
user compiling c file: opensource_libs/boringssl/err_data.c
create TA_MANIFEST_BIN: user/base/app/apploader/manifest.json to out/build-imx8mp/user_product/ta/apploader/apploader.manifest
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_bitstr.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_bool.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_gentm.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_d2i_fp.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_mbstr.c
user compiling c file: kernel/rctee/lib/app_manifest/app_manifest.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_dup.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_i2d_fp.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_octet.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_object.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_strex.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_time.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/posix_time.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_int.c
/home/<USER>/codebase/trusty/prebuilts/build-tools/linux-x86/bin/py3-cmd build/scripts/manifest_compiler.py -iuser/base/app/apploader/manifest.json -o out/build-imx8mp/user_product/ta/apploader/apploader.manifest  --header-dir out/build-imx8mp/user_product/ta/apploader/constants/include \
--enable-shadow-call-stack --default-shadow-call-stack-size 4096
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/f_string.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_utctm.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/asn1_lib.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_strnid.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/a_type.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/asn_pack.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/asn1_par.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/tasn_enc.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/tasn_fre.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/tasn_dec.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/f_int.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/tasn_new.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/tasn_typ.c
user compiling c file: opensource_libs/boringssl/src/crypto/asn1/tasn_utl.c
user compiling c file: opensource_libs/boringssl/src/crypto/bio/bio.c
user compiling c file: opensource_libs/boringssl/src/crypto/bio/socket_helper.c
user compiling c file: opensource_libs/boringssl/src/crypto/base64/base64.c
user compiling c file: opensource_libs/boringssl/src/crypto/bio/bio_mem.c
user compiling c file: opensource_libs/boringssl/src/crypto/bio/errno.c
user compiling c file: opensource_libs/boringssl/src/crypto/bio/connect.c
user compiling c file: opensource_libs/boringssl/src/crypto/bio/file.c
user compiling c file: opensource_libs/boringssl/src/crypto/bio/fd.c
user compiling c file: opensource_libs/boringssl/src/crypto/bio/hexdump.c
user compiling c file: opensource_libs/boringssl/src/crypto/bio/pair.c
user compiling c file: opensource_libs/boringssl/src/crypto/bio/printf.c
user compiling c file: opensource_libs/boringssl/src/crypto/bio/socket.c
user compiling c file: opensource_libs/boringssl/src/crypto/bn_extra/bn_asn1.c
user compiling c file: opensource_libs/boringssl/src/crypto/blake2/blake2.c
user compiling c file: opensource_libs/boringssl/src/crypto/bn_extra/convert.c
user compiling c file: opensource_libs/boringssl/src/crypto/bytestring/asn1_compat.c
user compiling c file: opensource_libs/boringssl/src/crypto/buf/buf.c
user compiling c file: opensource_libs/boringssl/src/crypto/bytestring/ber.c
user compiling c file: opensource_libs/boringssl/src/crypto/bytestring/cbb.c
user compiling c file: opensource_libs/boringssl/src/crypto/bytestring/cbs.c
user compiling c file: opensource_libs/boringssl/src/crypto/bytestring/unicode.c
user compiling c file: opensource_libs/boringssl/src/crypto/chacha/chacha.c
user compiling c file: opensource_libs/boringssl/src/crypto/cipher_extra/derive_key.c
user compiling c file: opensource_libs/boringssl/src/crypto/cipher_extra/cipher_extra.c
user compiling c file: opensource_libs/boringssl/src/crypto/cipher_extra/e_null.c
user compiling c file: opensource_libs/boringssl/src/crypto/cipher_extra/e_aesctrhmac.c
user compiling c file: opensource_libs/boringssl/src/crypto/cipher_extra/e_aesgcmsiv.c
user compiling c file: opensource_libs/boringssl/src/crypto/cipher_extra/e_chacha20poly1305.c
user compiling c file: opensource_libs/boringssl/src/crypto/cipher_extra/e_des.c
user compiling c file: opensource_libs/boringssl/src/crypto/cipher_extra/e_rc2.c
user compiling c file: opensource_libs/boringssl/src/crypto/cipher_extra/e_rc4.c
user compiling c file: opensource_libs/boringssl/src/crypto/cipher_extra/e_tls.c
user compiling c file: opensource_libs/boringssl/src/crypto/cipher_extra/tls_cbc.c
user compiling c file: opensource_libs/boringssl/src/crypto/cpu_aarch64_fuchsia.c
user compiling c file: opensource_libs/boringssl/src/crypto/cpu_aarch64_apple.c
user compiling c file: opensource_libs/boringssl/src/crypto/cpu_aarch64_linux.c
user compiling c file: opensource_libs/boringssl/src/crypto/cpu_aarch64_openbsd.c
user compiling c file: opensource_libs/boringssl/src/crypto/cpu_aarch64_sysreg.c
user compiling c file: opensource_libs/boringssl/src/crypto/cpu_aarch64_win.c
user compiling c file: opensource_libs/boringssl/src/crypto/conf/conf.c
user compiling c file: opensource_libs/boringssl/src/crypto/cpu_arm_freebsd.c
user compiling c file: opensource_libs/boringssl/src/crypto/cpu_arm_linux.c
user compiling c file: opensource_libs/boringssl/src/crypto/cpu_intel.c
user compiling c file: opensource_libs/boringssl/src/crypto/crypto.c
user compiling c file: opensource_libs/boringssl/src/crypto/curve25519/spake25519.c
user compiling c file: opensource_libs/boringssl/src/crypto/curve25519/curve25519.c
user compiling c file: opensource_libs/boringssl/src/crypto/curve25519/curve25519_64_adx.c
user compiling c file: opensource_libs/boringssl/src/crypto/des/des.c
user compiling c file: opensource_libs/boringssl/src/crypto/dh_extra/dh_asn1.c
user compiling c file: opensource_libs/boringssl/src/crypto/dh_extra/params.c
user compiling c file: opensource_libs/boringssl/src/crypto/digest_extra/digest_extra.c
user compiling c file: opensource_libs/boringssl/src/crypto/dsa/dsa.c
user compiling c file: opensource_libs/boringssl/src/crypto/dsa/dsa_asn1.c
user compiling c file: opensource_libs/boringssl/src/crypto/ec_extra/hash_to_curve.c
user compiling c file: opensource_libs/boringssl/src/crypto/ec_extra/ec_asn1.c
user compiling c file: opensource_libs/boringssl/src/crypto/ec_extra/ec_derive.c
user compiling c file: opensource_libs/boringssl/src/crypto/ecdh_extra/ecdh_extra.c
user compiling c file: opensource_libs/boringssl/src/crypto/ecdsa_extra/ecdsa_asn1.c
user compiling c file: opensource_libs/boringssl/src/crypto/engine/engine.c
user compiling c file: opensource_libs/boringssl/src/crypto/err/err.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/evp.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/evp_asn1.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/evp_ctx.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/p_dsa_asn1.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/p_ec.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/p_ec_asn1.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/p_ed25519.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/p_ed25519_asn1.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/p_hkdf.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/p_rsa.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/p_rsa_asn1.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/p_x25519.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/p_x25519_asn1.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/pbkdf.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/print.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/scrypt.c
user compiling c file: opensource_libs/boringssl/src/crypto/evp/sign.c
user compiling c file: opensource_libs/boringssl/src/crypto/ex_data.c
user compiling c file: opensource_libs/boringssl/src/crypto/fipsmodule/fips_shared_support.c
user compiling c file: opensource_libs/boringssl/src/crypto/fipsmodule/bcm.c
user compiling c file: opensource_libs/boringssl/src/crypto/hpke/hpke.c
user compiling c file: opensource_libs/boringssl/src/crypto/mem.c
user compiling c file: opensource_libs/boringssl/src/crypto/kyber/keccak.c
user compiling c file: opensource_libs/boringssl/src/crypto/hrss/hrss.c
user compiling c file: opensource_libs/boringssl/src/crypto/kyber/kyber.c
user compiling c file: opensource_libs/boringssl/src/crypto/lhash/lhash.c
user compiling c file: opensource_libs/boringssl/src/crypto/obj/obj_xref.c
user compiling c file: opensource_libs/boringssl/src/crypto/obj/obj.c
user compiling c file: opensource_libs/boringssl/src/crypto/pem/pem_all.c
user compiling c file: opensource_libs/boringssl/src/crypto/pem/pem_info.c
user compiling c file: opensource_libs/boringssl/src/crypto/pem/pem_lib.c
user compiling c file: opensource_libs/boringssl/src/crypto/pem/pem_pk8.c
user compiling c file: opensource_libs/boringssl/src/crypto/pem/pem_oth.c
user compiling c file: opensource_libs/boringssl/src/crypto/pem/pem_pkey.c
user compiling c file: opensource_libs/boringssl/src/crypto/pem/pem_x509.c
user compiling c file: opensource_libs/boringssl/src/crypto/pem/pem_xaux.c
user compiling c file: opensource_libs/boringssl/src/crypto/pkcs7/pkcs7.c
user compiling c file: opensource_libs/boringssl/src/crypto/pkcs8/pkcs8_x509.c
user compiling c file: opensource_libs/boringssl/src/crypto/pkcs7/pkcs7_x509.c
user compiling c file: opensource_libs/boringssl/src/crypto/pkcs8/p5_pbev2.c
user compiling c file: opensource_libs/boringssl/src/crypto/pkcs8/pkcs8.c
user compiling c file: opensource_libs/boringssl/src/crypto/poly1305/poly1305_vec.c
user compiling c file: opensource_libs/boringssl/src/crypto/poly1305/poly1305.c
user compiling c file: opensource_libs/boringssl/src/crypto/pool/pool.c
user compiling c file: opensource_libs/boringssl/src/crypto/poly1305/poly1305_arm.c
user compiling c file: opensource_libs/boringssl/src/crypto/rand_extra/deterministic.c
user compiling c file: opensource_libs/boringssl/src/crypto/rand_extra/forkunsafe.c
user compiling c file: opensource_libs/boringssl/src/crypto/rand_extra/getentropy.c
user compiling c file: opensource_libs/boringssl/src/crypto/rand_extra/passive.c
user compiling c file: opensource_libs/boringssl/src/crypto/rand_extra/ios.c
user compiling c file: opensource_libs/boringssl/src/crypto/rand_extra/rand_extra.c
user compiling c file: opensource_libs/boringssl/src/crypto/rand_extra/windows.c
user compiling c file: opensource_libs/boringssl/src/crypto/rand_extra/trusty.c
user compiling c file: opensource_libs/boringssl/src/crypto/refcount.c
user compiling c file: opensource_libs/boringssl/src/crypto/rc4/rc4.c
user compiling c file: opensource_libs/boringssl/src/crypto/rsa_extra/rsa_crypt.c
user compiling c file: opensource_libs/boringssl/src/crypto/rsa_extra/rsa_asn1.c
user compiling c file: opensource_libs/boringssl/src/crypto/rsa_extra/rsa_print.c
user compiling c file: opensource_libs/boringssl/src/crypto/stack/stack.c
user compiling c file: opensource_libs/boringssl/src/crypto/siphash/siphash.c
user compiling c file: opensource_libs/boringssl/src/crypto/thread.c
user compiling c file: opensource_libs/boringssl/src/crypto/thread_none.c
user compiling c file: opensource_libs/boringssl/src/crypto/thread_pthread.c
user compiling c file: opensource_libs/boringssl/src/crypto/thread_win.c
user compiling c file: opensource_libs/boringssl/src/crypto/trust_token/pmbtoken.c
user compiling c file: opensource_libs/boringssl/src/crypto/trust_token/voprf.c
user compiling c file: opensource_libs/boringssl/src/crypto/trust_token/trust_token.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/a_digest.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/a_sign.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/algorithm.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/asn1_gen.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/a_verify.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/by_dir.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/by_file.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/i2d_pr.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/name_print.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/policy.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/rsa_pss.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/t_crl.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/t_req.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/t_x509.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/t_x509a.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_att.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_cmp.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_d2.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_def.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_lu.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_ext.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_obj.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_set.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_req.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_trs.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_txt.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_v3.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_vfy.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509_vpm.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509cset.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509name.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509rset.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x509spki.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_algor.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_all.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_exten.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_crl.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_attrib.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_info.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_name.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_pkey.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_pubkey.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_req.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_sig.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_spki.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_val.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_x509a.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509/x_x509.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_akey.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_akeya.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_alt.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_bcons.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_bitst.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_conf.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_cpols.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_enum.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_crld.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_extku.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_ia5.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_genn.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_info.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_int.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_ncons.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_lib.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_ocsp.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_pcons.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_pmaps.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_prn.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_purp.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_skey.c
user compiling c file: opensource_libs/boringssl/src/crypto/x509v3/v3_utl.c
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/chacha/chacha-armv8-apple.S
user compiling c file: kernel/hardware/nxp/user/lib/nxp_openssl_stub/rand.c
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/cipher_extra/chacha20_poly1305_armv8-apple.S
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/aesv8-armv8-apple.S
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/aesv8-gcm-armv8-apple.S
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/armv8-mont-apple.S
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/bn-armv8-apple.S
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/ghashv8-armv8-apple.S
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/ghash-neon-armv8-apple.S
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/p256-armv8-asm-apple.S
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/p256_beeu-armv8-asm-apple.S
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/sha1-armv8-apple.S
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/sha256-armv8-apple.S
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/sha512-armv8-apple.S
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/vpaes-armv8-apple.S
user compiling asm file: opensource_libs/boringssl/apple-aarch64/crypto/test/trampoline-armv8-apple.S
user compiling asm file: opensource_libs/boringssl/apple-arm/crypto/chacha/chacha-armv4-apple.S
user compiling asm file: opensource_libs/boringssl/apple-arm/crypto/fipsmodule/armv4-mont-apple.S
user compiling asm file: opensource_libs/boringssl/apple-arm/crypto/fipsmodule/aesv8-armv7-apple.S
user compiling asm file: opensource_libs/boringssl/apple-arm/crypto/fipsmodule/ghash-armv4-apple.S
user compiling asm file: opensource_libs/boringssl/apple-arm/crypto/fipsmodule/bsaes-armv7-apple.S
user compiling asm file: opensource_libs/boringssl/apple-arm/crypto/fipsmodule/ghashv8-armv7-apple.S
user compiling asm file: opensource_libs/boringssl/apple-arm/crypto/fipsmodule/sha1-armv4-large-apple.S
user compiling asm file: opensource_libs/boringssl/apple-arm/crypto/fipsmodule/sha512-armv4-apple.S
user compiling asm file: opensource_libs/boringssl/apple-arm/crypto/fipsmodule/sha256-armv4-apple.S
user compiling asm file: opensource_libs/boringssl/apple-arm/crypto/fipsmodule/vpaes-armv7-apple.S
user compiling asm file: opensource_libs/boringssl/apple-arm/crypto/test/trampoline-armv4-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86/crypto/chacha/chacha-x86-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86/crypto/fipsmodule/aesni-x86-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86/crypto/fipsmodule/bn-586-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86/crypto/fipsmodule/co-586-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86/crypto/fipsmodule/ghash-ssse3-x86-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86/crypto/fipsmodule/ghash-x86-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86/crypto/fipsmodule/sha1-586-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86/crypto/fipsmodule/md5-586-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86/crypto/fipsmodule/sha256-586-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86/crypto/fipsmodule/sha512-586-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86/crypto/fipsmodule/x86-mont-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86/crypto/fipsmodule/vpaes-x86-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86/crypto/test/trampoline-x86-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/chacha/chacha-x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/cipher_extra/aes128gcmsiv-x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/cipher_extra/chacha20_poly1305_x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/aesni-gcm-x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/aesni-x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/md5-x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/ghash-ssse3-x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/ghash-x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/p256-x86_64-asm-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/p256_beeu-x86_64-asm-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/rdrand-x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/rsaz-avx2-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/sha1-x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/sha256-x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/sha512-x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/vpaes-x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/x86_64-mont-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/x86_64-mont5-apple.S
user compiling asm file: opensource_libs/boringssl/apple-x86_64/crypto/test/trampoline-x86_64-apple.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/chacha/chacha-armv8-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/cipher_extra/chacha20_poly1305_armv8-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/aesv8-armv8-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/aesv8-gcm-armv8-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/armv8-mont-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/bn-armv8-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/ghash-neon-armv8-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/ghashv8-armv8-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/p256-armv8-asm-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/p256_beeu-armv8-asm-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/sha1-armv8-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/sha256-armv8-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/sha512-armv8-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/vpaes-armv8-linux.S
user compiling asm file: opensource_libs/boringssl/linux-aarch64/crypto/test/trampoline-armv8-linux.S
user compiling asm file: opensource_libs/boringssl/linux-arm/crypto/chacha/chacha-armv4-linux.S
user compiling asm file: opensource_libs/boringssl/linux-arm/crypto/fipsmodule/aesv8-armv7-linux.S
user compiling asm file: opensource_libs/boringssl/linux-arm/crypto/fipsmodule/armv4-mont-linux.S
user compiling asm file: opensource_libs/boringssl/linux-arm/crypto/fipsmodule/bsaes-armv7-linux.S
user compiling asm file: opensource_libs/boringssl/linux-arm/crypto/fipsmodule/ghash-armv4-linux.S
user compiling asm file: opensource_libs/boringssl/linux-arm/crypto/fipsmodule/ghashv8-armv7-linux.S
user compiling asm file: opensource_libs/boringssl/linux-arm/crypto/fipsmodule/sha1-armv4-large-linux.S
user compiling asm file: opensource_libs/boringssl/linux-arm/crypto/fipsmodule/sha256-armv4-linux.S
user compiling asm file: opensource_libs/boringssl/linux-arm/crypto/fipsmodule/sha512-armv4-linux.S
user compiling asm file: opensource_libs/boringssl/linux-arm/crypto/fipsmodule/vpaes-armv7-linux.S
user compiling asm file: opensource_libs/boringssl/linux-arm/crypto/test/trampoline-armv4-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86/crypto/chacha/chacha-x86-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86/crypto/fipsmodule/md5-586-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86/crypto/fipsmodule/aesni-x86-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86/crypto/fipsmodule/bn-586-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86/crypto/fipsmodule/ghash-ssse3-x86-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86/crypto/fipsmodule/co-586-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86/crypto/fipsmodule/ghash-x86-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86/crypto/fipsmodule/sha1-586-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86/crypto/fipsmodule/sha256-586-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86/crypto/fipsmodule/sha512-586-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86/crypto/fipsmodule/vpaes-x86-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86/crypto/fipsmodule/x86-mont-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/chacha/chacha-x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/cipher_extra/aes128gcmsiv-x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/md5-x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/aesni-gcm-x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86/crypto/test/trampoline-x86-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/cipher_extra/chacha20_poly1305_x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/aesni-x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/ghash-ssse3-x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/ghash-x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/p256-x86_64-asm-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/p256_beeu-x86_64-asm-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/rdrand-x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/rsaz-avx2-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/sha1-x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/sha256-x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/sha512-x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/vpaes-x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/x86_64-mont-linux.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/x86_64-mont5-linux.S
user compiling asm file: opensource_libs/boringssl/src/crypto/poly1305/poly1305_arm_asm.S
user compiling asm file: opensource_libs/boringssl/linux-x86_64/crypto/test/trampoline-x86_64-linux.S
user compiling asm file: opensource_libs/boringssl/src/crypto/curve25519/asm/x25519-asm-arm.S
user compiling asm file: opensource_libs/boringssl/src/crypto/hrss/asm/poly_rq_mul.S
user compiling asm file: opensource_libs/boringssl/src/third_party/fiat/asm/fiat_curve25519_adx_mul.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/chacha/chacha-armv8-win.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/cipher_extra/chacha20_poly1305_armv8-win.S
user compiling asm file: opensource_libs/boringssl/src/third_party/fiat/asm/fiat_curve25519_adx_square.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/aesv8-armv8-win.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/aesv8-gcm-armv8-win.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/armv8-mont-win.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/bn-armv8-win.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/ghash-neon-armv8-win.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/ghashv8-armv8-win.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/p256-armv8-asm-win.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/sha1-armv8-win.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/p256_beeu-armv8-asm-win.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/sha256-armv8-win.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/sha512-armv8-win.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/vpaes-armv8-win.S
user compiling asm file: opensource_libs/boringssl/win-aarch64/crypto/test/trampoline-armv8-win.S
user compiling c file: user/base/lib/rng/trusty_rng.c
user compiling c file: opensource_libs/open-dice/src/android.c
user compiling c file: opensource_libs/open-dice/src/boringssl_hash_kdf_ops.c
user compiling c file: opensource_libs/open-dice/src/boringssl_ed25519_ops.c
user compiling c file: opensource_libs/open-dice/src/cbor_cert_op.c
user compiling c file: opensource_libs/open-dice/src/cbor_ed25519_cert_op.c
user compiling c file: opensource_libs/open-dice/src/cbor_reader.c
user compiling c file: opensource_libs/open-dice/src/cbor_writer.c
user compiling c file: opensource_libs/open-dice/src/clear_memory.c
user compiling c file: opensource_libs/open-dice/src/dice.c
user compiling c file: opensource_libs/open-dice/src/utils.c
user compiling cpp file: user/base/lib/apploader_package/cose.cpp
user compiling cpp file: user/base/lib/apploader_package/package.cpp
user compiling c file: user/base/lib/hwaes/hwaes.c
user compiling c file: user/base/lib/tipc/tipc.c
user compiling c file: user/base/lib/tipc/tipc_srv.c
user compiling c file: user/base/lib/hwkey/hwkey.c
user compiling c file: user/base/lib/dlmalloc/malloc.c
generating syscalls stubs out/build-imx8mp/generated/user/base/lib/syscall-stubs/rctee_syscalls.S
user compiling c file: kernel/rctee/lib/libc-ext/scnprintf.c
user compiling c file: kernel/rctee/lib/libc-ext/uuid.c
user compiling c file: opensource_libs/musl/crt/rcrt1.c
user compiling cpp file: opensource_libs/libcxxabi/src/cxa_aux_runtime.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/cxa_default_handlers.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/cxa_demangle.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/cxa_exception_storage.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/cxa_guard.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/cxa_handlers.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/cxa_unexpected.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/cxa_vector.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/cxa_virtual.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/stdlib_exception.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/stdlib_stdexcept.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/stdlib_typeinfo.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/abort_message.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/fallback_malloc.cpp
user compiling cpp file: opensource_libs/libcxxabi/src/cxa_noexception.cpp
user compiling cpp file: opensource_libs/libcxx/src/algorithm.cpp
user compiling cpp file: opensource_libs/libcxx/src/any.cpp
user compiling cpp file: opensource_libs/libcxx/src/bind.cpp
user compiling cpp file: opensource_libs/libcxx/src/charconv.cpp
user compiling cpp file: opensource_libs/libcxx/src/chrono.cpp
user compiling cpp file: opensource_libs/libcxx/src/condition_variable.cpp
user compiling cpp file: opensource_libs/libcxx/src/debug.cpp
user compiling cpp file: opensource_libs/libcxx/src/exception.cpp
user compiling cpp file: opensource_libs/libcxx/src/future.cpp
user compiling cpp file: opensource_libs/libcxx/src/hash.cpp
user compiling cpp file: opensource_libs/libcxx/src/ios.cpp
user compiling cpp file: opensource_libs/libcxx/src/iostream.cpp
user compiling cpp file: opensource_libs/libcxx/src/locale.cpp
user compiling cpp file: opensource_libs/libcxx/src/memory.cpp
user compiling cpp file: opensource_libs/libcxx/src/mutex.cpp
user compiling cpp file: opensource_libs/libcxx/src/regex.cpp
user compiling cpp file: opensource_libs/libcxx/src/optional.cpp
user compiling cpp file: opensource_libs/libcxx/src/new.cpp
user compiling cpp file: opensource_libs/libcxx/src/shared_mutex.cpp
user compiling cpp file: opensource_libs/libcxx/src/string.cpp
user compiling cpp file: opensource_libs/libcxx/src/strstream.cpp
user compiling cpp file: opensource_libs/libcxx/src/system_error.cpp
user compiling cpp file: opensource_libs/libcxx/src/thread.cpp
user compiling cpp file: opensource_libs/libcxx/src/typeinfo.cpp
user compiling cpp file: opensource_libs/libcxx/src/utility.cpp
user compiling cpp file: opensource_libs/libcxx/src/valarray.cpp
user compiling cpp file: opensource_libs/libcxx/src/variant.cpp
user compiling cpp file: opensource_libs/libcxx/src/vector.cpp
user compiling c file: user/base/lib/line-coverage/shm.c
user compiling c file: user/base/lib/coverage/common/ipc.c
user compiling c file: user/base/lib/coverage/common/cov_shm.c
user compiling cpp file: user/base/lib/sample/apploader_policy_engine/apploader_policy_engine.cpp
user compiling c file: user/base/lib/storage/storage.c
user compiling c file: user/base/lib/system_state/system_state.c
create TA_MANIFEST_BIN: kernel/hardware/nxp/app/hwcrypto/../../platform/imx/soc/imx8mp/include/nxp_hwcrypto_manifest.json to out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.manifest
user compiling c file: user/base/lib/hwaes/srv/hwaes_server.c
/home/<USER>/codebase/trusty/prebuilts/build-tools/linux-x86/bin/py3-cmd build/scripts/manifest_compiler.py -ikernel/hardware/nxp/app/hwcrypto/../../platform/imx/soc/imx8mp/include/nxp_hwcrypto_manifest.json -o out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.manifest -ckernel/hardware/nxp/app/hwcrypto/../../platform/imx/soc/imx8mp/include/nxp_hwcrypto_memmap_consts.json -ckernel/hardware/nxp/app/hwcrypto/nxp_hwcrypto_consts.json -ckernel/hardware/nxp/app/hwcrypto/nxp_hwcrypto_uuid_consts.json --header-dir out/build-imx8mp/user_product/ta/hwcrypto/constants/include \
--enable-shadow-call-stack --default-shadow-call-stack-size 4096
create TA_MANIFEST_BIN: user/app/sample/memref-test/receiver/manifest.json to out/build-imx8mp/user_product/ta/receiver/receiver.manifest
/home/<USER>/codebase/trusty/prebuilts/build-tools/linux-x86/bin/py3-cmd build/scripts/manifest_compiler.py -iuser/app/sample/memref-test/receiver/manifest.json -o out/build-imx8mp/user_product/ta/receiver/receiver.manifest  --header-dir out/build-imx8mp/user_product/ta/receiver/constants/include \
--enable-shadow-call-stack --default-shadow-call-stack-size 4096
create TA_MANIFEST_BIN: user/app/sample/manifest-test/manifest.json to out/build-imx8mp/user_product/ta/manifest-test/manifest-test.manifest
user compiling c file: user/base/lib/unittest/unittest.c
/home/<USER>/codebase/trusty/prebuilts/build-tools/linux-x86/bin/py3-cmd build/scripts/manifest_compiler.py -iuser/app/sample/manifest-test/manifest.json -o out/build-imx8mp/user_product/ta/manifest-test/manifest-test.manifest  --header-dir out/build-imx8mp/user_product/ta/manifest-test/constants/include \
--enable-shadow-call-stack --default-shadow-call-stack-size 4096
generating out/build-imx8mp/system-onesegment.ld
create TA_MANIFEST_BIN: user/app/test/tongsuo_test/manifest.json to out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.manifest
user compiling c file: opensource_libs/Tongsuo/crypto/sm2/sm2_sign.c
user compiling c file: opensource_libs/Tongsuo/crypto/sm2/sm2_crypt.c
user compiling c file: opensource_libs/Tongsuo/crypto/sm3/sm3.c
user compiling c file: opensource_libs/Tongsuo/crypto/sm4/sm4.c
user compiling c file: opensource_libs/Tongsuo/crypto/cryptlib.c
/home/<USER>/codebase/trusty/prebuilts/build-tools/linux-x86/bin/py3-cmd build/scripts/manifest_compiler.py -iuser/app/test/tongsuo_test/manifest.json -o out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.manifest  --header-dir out/build-imx8mp/user_product/ta/tongsuo_test/constants/include \
--enable-shadow-call-stack --default-shadow-call-stack-size 4096
user compiling c file: opensource_libs/Tongsuo/crypto/mem.c
user compiling c file: opensource_libs/Tongsuo/crypto/err/err.c
compiling kernel/hardware/nxp/platform/imx/debug.c
compiling kernel/hardware/nxp/platform/imx/platform.c
compiling kernel/hardware/nxp/platform/imx/smc_service_access_policy.c
compiling kernel/hardware/nxp/platform/imx/apploader_mmio_apps.c
compiling kernel/hardware/nxp/platform/imx/drivers/imx_caam.c
compiling kernel/hardware/nxp/platform/imx/drivers/imx_csu.c
compiling kernel/hardware/nxp/platform/imx/drivers/imx_lcdif.c
compiling kernel/hardware/nxp/platform/imx/drivers/imx_uart.c
compiling kernel/hardware/nxp/platform/imx/drivers/imx_snvs.c
compiling kernel/hardware/nxp/platform/imx/drivers/imx_vpu.c
compiling kernel/hardware/nxp/platform/imx/drivers/imx_vpu_enc.c
compiling kernel/lk/app/app.c
compiling kernel/lk/arch/arm64/arch.c
compiling kernel/lk/arch/arm64/exceptions_c.c
compiling kernel/lk/arch/arm64/fpu.c
compiling kernel/lk/arch/arm64/memtag.c
compiling kernel/lk/arch/arm64/thread.c
compiling kernel/lk/arch/arm64/pan.c
compiling kernel/lk/arch/arm64/bti.c
compiling kernel/lk/arch/arm64/pac.c
compiling kernel/lk/arch/arm64/sve.c
compiling kernel/lk/arch/arm64/mp.c
compiling kernel/lk/arch/arm64/early_mmu.c
compiling kernel/lk/arch/arm64/mmu.c
compiling kernel/lk/arch/arm64/asm.S
compiling kernel/lk/arch/arm64/spinlock.S
compiling kernel/lk/arch/arm64/start.S
compiling kernel/lk/arch/arm64/exceptions.S
compiling kernel/lk/arch/arm64/cache-ops.S
compiling kernel/lk/arch/arm64/usercopy.S
compiling kernel/lk/arch/arm64/safecopy.S
compiling kernel/lk/dev/driver.c
compiling kernel/lk/dev/dev.c
compiling kernel/lk/dev/class/block_api.c
compiling kernel/lk/dev/class/i2c_api.c
compiling kernel/lk/dev/class/spi_api.c
compiling kernel/lk/dev/class/uart_api.c
compiling kernel/lk/dev/class/fb_api.c
compiling kernel/lk/dev/class/netif_api.c
compiling kernel/lk/dev/interrupt/arm_gic/arm_gic.c
compiling kernel/lk/dev/interrupt/arm_gic/gic_v3.c
compiling kernel/lk/dev/timer/arm_generic/arm_generic_timer.c
compiling kernel/lk/kernel/debug.c
compiling kernel/lk/kernel/init.c
compiling kernel/lk/kernel/event.c
compiling kernel/lk/kernel/mutex.c
compiling kernel/lk/kernel/thread.c
compiling kernel/lk/kernel/timer.c
compiling kernel/lk/kernel/semaphore.c
compiling kernel/lk/kernel/mp.c
opensource_libs/Tongsuo/crypto/err/err.c:830:17: warning: assigning to 'char *' from 'const char[7]' discards qualifiers [-Wincompatible-pointer-types-discards-qualifiers]
            arg = "<NULL>";
                ^ ~~~~~~~~
compiling kernel/lk/kernel/port.c
compiling kernel/lk/kernel/vm/asid.c
compiling kernel/lk/kernel/vm/bootalloc.c
compiling kernel/lk/kernel/vm/mmu_common.c
compiling kernel/lk/kernel/vm/physmem.c
compiling kernel/lk/kernel/vm/pmm.c
compiling kernel/lk/kernel/vm/res_group.c
compiling kernel/lk/kernel/vm/vm.c
compiling kernel/lk/kernel/vm/relocate.c
compiling kernel/lk/kernel/vm/vmm.c
compiling kernel/lk/lib/binary_search_tree/binary_search_tree.c
compiling kernel/lk/lib/cbuf/cbuf.c
compiling kernel/lk/lib/debug/debug.c
compiling kernel/lk/lib/fixed_point/fixed_point.c
1 warning generated.
compiling kernel/lk/lib/heap/heap_wrapper.c
compiling kernel/lk/lib/heap/page_alloc.c
compiling kernel/lk/lib/io/console.c
compiling kernel/lk/lib/heap/miniheap/miniheap.c
compiling kernel/lk/lib/io/io.c
compiling kernel/lk/platform/debug.c
compiling kernel/lk/platform/init.c
compiling kernel/lk/platform/power.c
compiling kernel/lk/platform/random.c
compiling kernel/lk/target/init.c
compiling kernel/lk/top/init.c
compiling kernel/lk/top/main.c
compiling kernel/rctee/app/busytest/busytest.c
compiling kernel/rctee/lib/app_manifest/app_manifest.c
compiling kernel/rctee/lib/arm_ffa/arm_ffa.c
compiling kernel/rctee/lib/backtrace/backtrace.c
compiling kernel/rctee/lib/backtrace/symbolize.c
compiling kernel/rctee/lib/backtrace/arch/arm64/backtrace.c
compiling kernel/rctee/lib/ktipc/ktipc.c
compiling kernel/rctee/lib/ktipc/test/main/main.c
compiling kernel/rctee/lib/extmem/external_memory.c
compiling kernel/rctee/lib/ktipc/test/srv/srv.c
compiling kernel/rctee/lib/libc-ext/scnprintf.c
compiling kernel/rctee/lib/libc-ext/uuid.c
compiling kernel/rctee/lib/libc-trusty/abort.c
compiling kernel/rctee/lib/libc-trusty/close.c
compiling kernel/rctee/lib/libc-trusty/fflush.c
compiling kernel/rctee/lib/libc-trusty/libc_state.c
compiling kernel/rctee/lib/libc-trusty/writev.c
compiling kernel/lk/lib/libc/atoi.c
compiling kernel/lk/lib/libc/eabi.c
compiling kernel/lk/lib/libc/eabi_unwind_stubs.c
compiling kernel/lk/lib/libc/io_handle.c
compiling kernel/lk/lib/libc/printf.c
compiling kernel/lk/lib/libc/rand.c
compiling kernel/lk/lib/libc/stdio.c
compiling kernel/lk/lib/libc/strtol.c
compiling kernel/lk/lib/libc/strtoll.c
compiling user/base/lib/libc-rctee/locale_stubs.c
compiling kernel/lk/lib/libc/atexit.c
compiling user/base/lib/libc-rctee/pthreads.c
compiling opensource_libs/musl/src/ctype/isascii.c
compiling opensource_libs/musl/src/ctype/isalnum.c
compiling opensource_libs/musl/src/ctype/isblank.c
compiling opensource_libs/musl/src/ctype/isalpha.c
compiling opensource_libs/musl/src/ctype/iscntrl.c
compiling opensource_libs/musl/src/ctype/isdigit.c
compiling opensource_libs/musl/src/ctype/isgraph.c
compiling opensource_libs/musl/src/ctype/islower.c
compiling opensource_libs/musl/src/ctype/isprint.c
compiling opensource_libs/musl/src/ctype/ispunct.c
compiling opensource_libs/musl/src/ctype/isspace.c
compiling opensource_libs/musl/src/ctype/isupper.c
compiling opensource_libs/musl/src/ctype/isxdigit.c
compiling opensource_libs/musl/src/ctype/toascii.c
compiling opensource_libs/musl/src/ctype/tolower.c
compiling opensource_libs/musl/src/ctype/toupper.c
compiling opensource_libs/musl/src/locale/c_locale.c
compiling opensource_libs/musl/src/stdlib/imaxabs.c
compiling opensource_libs/musl/src/stdlib/abs.c
compiling opensource_libs/musl/src/stdlib/div.c
compiling opensource_libs/musl/src/stdlib/bsearch.c
compiling opensource_libs/musl/src/stdlib/imaxdiv.c
compiling opensource_libs/musl/src/stdlib/labs.c
compiling opensource_libs/musl/src/stdlib/ldiv.c
compiling opensource_libs/musl/src/stdlib/llabs.c
compiling opensource_libs/musl/src/stdlib/lldiv.c
compiling opensource_libs/musl/src/stdlib/qsort.c
compiling opensource_libs/musl/src/string/bcmp.c
compiling opensource_libs/musl/src/string/memccpy.c
compiling opensource_libs/musl/src/string/memmem.c
compiling opensource_libs/musl/src/string/memrchr.c
compiling opensource_libs/musl/src/string/stpcpy.c
compiling opensource_libs/musl/src/string/stpncpy.c
compiling opensource_libs/musl/src/string/mempcpy.c
compiling opensource_libs/musl/src/string/strcasestr.c
compiling opensource_libs/musl/src/string/strcasecmp.c
compiling opensource_libs/musl/src/string/strchrnul.c
compiling opensource_libs/musl/src/string/strcspn.c
compiling opensource_libs/musl/src/string/strerror_r.c
compiling opensource_libs/musl/src/string/strncasecmp.c
compiling opensource_libs/musl/src/string/strndup.c
compiling opensource_libs/musl/src/string/strsep.c
compiling opensource_libs/musl/src/string/strtok_r.c
compiling opensource_libs/musl/src/string/strverscmp.c
compiling opensource_libs/musl/src/string/swab.c
compiling opensource_libs/musl/src/stdio/stderr.c
compiling opensource_libs/musl/src/stdio/stdout.c
compiling opensource_libs/musl/src/stdio/stdin.c
compiling opensource_libs/musl/src/stdio/__stdio_close.c
compiling opensource_libs/musl/src/stdio/__stdio_read.c
compiling opensource_libs/musl/src/stdio/__stdio_write.c
compiling opensource_libs/musl/src/stdio/__stdio_seek.c
compiling opensource_libs/musl/src/ctype/__ctype_get_mb_cur_max.c
compiling opensource_libs/musl/src/multibyte/internal.c
compiling opensource_libs/musl/src/multibyte/mbtowc.c
compiling opensource_libs/musl/src/multibyte/wcrtomb.c
compiling kernel/lk/lib/libc/string/bcopy.c
compiling kernel/lk/lib/libc/string/bzero.c
compiling kernel/lk/lib/libc/string/memchr.c
compiling kernel/lk/lib/libc/string/memcpy.c
compiling kernel/lk/lib/libc/string/memcmp.c
compiling kernel/lk/lib/libc/string/memmove.c
compiling kernel/lk/lib/libc/string/memset.c
compiling kernel/lk/lib/libc/string/strcat.c
compiling kernel/lk/lib/libc/string/strchr.c
compiling kernel/lk/lib/libc/string/strcmp.c
compiling kernel/lk/lib/libc/string/strcoll.c
compiling kernel/lk/lib/libc/string/strcpy.c
compiling kernel/lk/lib/libc/string/strdup.c
compiling kernel/lk/lib/libc/string/strerror.c
compiling kernel/lk/lib/libc/string/strlcat.c
compiling kernel/lk/lib/libc/string/strlen.c
compiling kernel/lk/lib/libc/string/strlcpy.c
compiling kernel/lk/lib/libc/string/strncat.c
compiling kernel/lk/lib/libc/string/strncpy.c
compiling kernel/lk/lib/libc/string/strncmp.c
compiling kernel/lk/lib/libc/string/strnicmp.c
compiling kernel/lk/lib/libc/string/strnlen.c
compiling kernel/lk/lib/libc/string/strpbrk.c
compiling kernel/lk/lib/libc/string/strrchr.c
compiling kernel/lk/lib/libc/string/strspn.c
compiling kernel/lk/lib/libc/string/strtok.c
compiling kernel/lk/lib/libc/string/strstr.c
compiling kernel/lk/lib/libc/string/strxfrm.c
compiling kernel/lk/lib/libc/pure_virtual.cpp
compiling kernel/rctee/lib/memlog/memlog.c
compiling kernel/rctee/lib/rand/rand.c
compiling kernel/rctee/lib/rctee/rctee_core/event.c
compiling kernel/rctee/lib/rctee/rctee_core/handle.c
compiling kernel/rctee/lib/rctee/rctee_core/handle_set.c
compiling kernel/rctee/lib/rctee/rctee_core/iovec.c
compiling kernel/rctee/lib/rctee/rctee_core/ipc_msg.c
compiling kernel/rctee/lib/rctee/rctee_core/ipc.c
compiling kernel/rctee/lib/rctee/rctee_core/memref.c
compiling kernel/rctee/lib/rctee/rctee_core/syscall.c
compiling kernel/rctee/lib/rctee/rctee_core/uctx.c
compiling kernel/rctee/lib/rctee/rctee_core/uirq.c
compiling kernel/rctee/lib/rctee/rctee_core/util.c
compiling kernel/rctee/lib/rctee/rctee_core/uuid.c
compiling kernel/rctee/lib/rctee/rctee_core/rctee_app.c
compiling kernel/rctee/lib/rctee/rctee_vitio/vqueue.c
compiling kernel/rctee/lib/rctee/rctee_vitio/smcall.c
compiling kernel/rctee/lib/rctee/rctee_vitio/rcipc_dev_ql.c
compiling kernel/rctee/lib/rctee/rctee_vitio/rcipc_virtio_dev.c
compiling kernel/rctee/lib/rctee/rctee_vitio/rctee_virtio.c
compiling kernel/rctee/lib/rctee/rcipc_config.c
compiling kernel/rctee/lib/sm/sm.c
compiling kernel/rctee/lib/sm/smcall.c
compiling kernel/rctee/lib/sm/ns_mem.c
compiling kernel/rctee/lib/sm/shared_mem.c
compiling kernel/rctee/lib/sm/trusty_sched_share.c
compiling kernel/rctee/lib/syscall/syscall.c
compiling kernel/rctee/lib/sm/arch/arm64/entry.S
compiling kernel/rctee/lib/smc/arch/arm64/smc.S
compiling kernel/rctee/lib/unittest/unittest.c
compiling kernel/rctee/lib/syscall/arch/arm64/syscall.S
compiling kernel/rctee/lib/ubsan/ubsan.c
compiling kernel/rctee/services/apploader/apploader_service.c
compiling kernel/rctee/services/generic_ta_service/generic_ta_service.c
compiling kernel/rctee/services/hwrng/hwrng_service.c
compiling kernel/rctee/services/smc/smc_service.c
user compiling c file: user/base/app/apploader/apploader.c
user compiling cpp file: user/base/app/apploader/app_version.cpp
user compiling cpp file: user/base/app/apploader/app_manifest_parser.cpp
creating static lib: out/build-imx8mp/user_product/lib/app_manifest/libapp_manifest.a
creating static lib: out/build-imx8mp/user_product/lib/rng/librng.a
creating static lib: out/build-imx8mp/user_product/lib/tipc/libtipc.a
creating static lib: out/build-imx8mp/user_product/lib/hwkey/libhwkey.a
creating static lib: out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a
user compiling c file: kernel/lk/lib/libc/eabi_unwind_stubs.c
user compiling c file: user/base/lib/libc-rctee/__set_thread_area.c
user compiling c file: user/base/lib/libc-rctee/__dso_handle.c
user compiling c file: user/base/lib/libc-rctee/file_stubs.c
user compiling c file: user/base/lib/libc-rctee/locale_stubs.c
user compiling c file: user/base/lib/libc-rctee/time_stubs.c
user compiling c file: user/base/lib/libc-rctee/ipc.c
user compiling c file: user/base/lib/libc-rctee/logging.c
user compiling c file: user/base/lib/libc-rctee/memref.c
user compiling c file: user/base/lib/libc-rctee/mman.c
user compiling c file: user/base/lib/libc-rctee/time.c
user compiling c file: user/base/lib/libc-rctee/rctee_err.c
user compiling c file: user/base/lib/libc-rctee/rctee_uio.c
user compiling c file: opensource_libs/musl/src/env/__init_tls.c
user compiling c file: opensource_libs/musl/src/env/__environ.c
user compiling c file: opensource_libs/musl/src/env/__libc_start_main.c
user compiling c file: opensource_libs/musl/src/env/__stack_chk_fail.c
user compiling c file: opensource_libs/musl/src/internal/intscan.c
user compiling c file: opensource_libs/musl/src/env/getenv.c
user compiling c file: opensource_libs/musl/src/internal/defsysinfo.c
user compiling c file: opensource_libs/musl/src/internal/floatscan.c
user compiling c file: opensource_libs/musl/src/internal/libc.c
user compiling c file: opensource_libs/musl/src/internal/shgetc.c
user compiling c file: opensource_libs/musl/src/ctype/__ctype_b_loc.c
user compiling c file: opensource_libs/musl/src/ctype/__ctype_get_mb_cur_max.c
user compiling c file: opensource_libs/musl/src/ctype/__ctype_tolower_loc.c
user compiling c file: opensource_libs/musl/src/ctype/isalpha.c
user compiling c file: opensource_libs/musl/src/ctype/isascii.c
user compiling c file: opensource_libs/musl/src/ctype/isblank.c
user compiling c file: opensource_libs/musl/src/ctype/iscntrl.c
user compiling c file: opensource_libs/musl/src/ctype/__ctype_toupper_loc.c
user compiling c file: opensource_libs/musl/src/ctype/isalnum.c
user compiling c file: opensource_libs/musl/src/ctype/isdigit.c
user compiling c file: opensource_libs/musl/src/ctype/isgraph.c
user compiling c file: opensource_libs/musl/src/ctype/islower.c
user compiling c file: opensource_libs/musl/src/ctype/isprint.c
user compiling c file: opensource_libs/musl/src/ctype/ispunct.c
user compiling c file: opensource_libs/musl/src/ctype/isspace.c
user compiling c file: opensource_libs/musl/src/ctype/isupper.c
user compiling c file: opensource_libs/musl/src/ctype/iswalnum.c
user compiling c file: opensource_libs/musl/src/ctype/iswalpha.c
user compiling c file: opensource_libs/musl/src/ctype/iswblank.c
user compiling c file: opensource_libs/musl/src/ctype/iswcntrl.c
user compiling c file: opensource_libs/musl/src/ctype/iswctype.c
user compiling c file: opensource_libs/musl/src/ctype/iswdigit.c
user compiling c file: opensource_libs/musl/src/ctype/iswgraph.c
user compiling c file: opensource_libs/musl/src/ctype/iswlower.c
user compiling c file: opensource_libs/musl/src/ctype/iswprint.c
user compiling c file: opensource_libs/musl/src/ctype/iswpunct.c
user compiling c file: opensource_libs/musl/src/ctype/iswspace.c
user compiling c file: opensource_libs/musl/src/ctype/iswupper.c
In file included from kernel/rctee/lib/sm/sm.c:43:
kernel/rctee/lib/sm/include/lib/sm/smc.h:32:9: warning: lib/sm/smc.h was moved to lib/smc, please use the new path [-W#pragma-messages]
#pragma message "lib/sm/smc.h was moved to lib/smc, please use the new path"
        ^
user compiling c file: opensource_libs/musl/src/ctype/iswxdigit.c
user compiling c file: opensource_libs/musl/src/ctype/toascii.c
user compiling c file: opensource_libs/musl/src/ctype/isxdigit.c
user compiling c file: opensource_libs/musl/src/ctype/tolower.c
user compiling c file: opensource_libs/musl/src/ctype/toupper.c
user compiling c file: opensource_libs/musl/src/ctype/towctrans.c
user compiling c file: opensource_libs/musl/src/ctype/wcswidth.c
user compiling c file: opensource_libs/musl/src/ctype/wctrans.c
user compiling c file: opensource_libs/musl/src/ctype/wcwidth.c
user compiling c file: opensource_libs/musl/src/errno/strerror.c
user compiling c file: opensource_libs/musl/src/errno/__errno_location.c
user compiling c file: opensource_libs/musl/src/exit/abort.c
user compiling c file: opensource_libs/musl/src/exit/assert.c
user compiling c file: opensource_libs/musl/src/exit/atexit.c
user compiling c file: opensource_libs/musl/src/exit/exit.c
user compiling c file: opensource_libs/musl/src/exit/_Exit.c
user compiling c file: opensource_libs/musl/src/misc/getauxval.c
user compiling c file: opensource_libs/musl/src/multibyte/btowc.c
user compiling c file: opensource_libs/musl/src/multibyte/c16rtomb.c
user compiling c file: opensource_libs/musl/src/multibyte/c32rtomb.c
user compiling c file: opensource_libs/musl/src/multibyte/internal.c
user compiling c file: opensource_libs/musl/src/multibyte/mblen.c
user compiling c file: opensource_libs/musl/src/multibyte/mbrlen.c
user compiling c file: opensource_libs/musl/src/multibyte/mbrtoc16.c
user compiling c file: opensource_libs/musl/src/multibyte/mbrtoc32.c
user compiling c file: opensource_libs/musl/src/multibyte/mbrtowc.c
user compiling c file: opensource_libs/musl/src/multibyte/mbsinit.c
user compiling c file: opensource_libs/musl/src/multibyte/mbsnrtowcs.c
user compiling c file: opensource_libs/musl/src/multibyte/mbsrtowcs.c
user compiling c file: opensource_libs/musl/src/multibyte/mbstowcs.c
user compiling c file: opensource_libs/musl/src/multibyte/mbtowc.c
user compiling c file: opensource_libs/musl/src/multibyte/wcrtomb.c
user compiling c file: opensource_libs/musl/src/multibyte/wcsnrtombs.c
user compiling c file: opensource_libs/musl/src/multibyte/wcsrtombs.c
user compiling c file: opensource_libs/musl/src/multibyte/wcstombs.c
user compiling c file: opensource_libs/musl/src/multibyte/wctob.c
user compiling c file: opensource_libs/musl/src/multibyte/wctomb.c
user compiling c file: opensource_libs/musl/src/network/htonl.c
user compiling c file: opensource_libs/musl/src/network/htons.c
user compiling c file: opensource_libs/musl/src/network/ntohs.c
user compiling c file: opensource_libs/musl/src/network/ntohl.c
user compiling c file: opensource_libs/musl/src/prng/rand.c
user compiling c file: opensource_libs/musl/src/stdlib/atof.c
user compiling c file: opensource_libs/musl/src/stdlib/abs.c
user compiling c file: opensource_libs/musl/src/stdlib/atoi.c
user compiling c file: opensource_libs/musl/src/stdlib/atoll.c
user compiling c file: opensource_libs/musl/src/stdlib/atol.c
user compiling c file: opensource_libs/musl/src/stdlib/bsearch.c
user compiling c file: opensource_libs/musl/src/stdlib/div.c
user compiling c file: opensource_libs/musl/src/stdlib/ecvt.c
user compiling c file: opensource_libs/musl/src/stdlib/gcvt.c
user compiling c file: opensource_libs/musl/src/stdlib/fcvt.c
user compiling c file: opensource_libs/musl/src/stdlib/imaxabs.c
user compiling c file: opensource_libs/musl/src/stdlib/imaxdiv.c
user compiling c file: opensource_libs/musl/src/stdlib/labs.c
user compiling c file: opensource_libs/musl/src/stdlib/ldiv.c
user compiling c file: opensource_libs/musl/src/stdlib/lldiv.c
user compiling c file: opensource_libs/musl/src/stdlib/llabs.c
user compiling c file: opensource_libs/musl/src/stdlib/qsort.c
1 warning generated.
user compiling c file: opensource_libs/musl/src/stdlib/strtod.c
user compiling c file: opensource_libs/musl/src/stdlib/strtol.c
user compiling c file: opensource_libs/musl/src/stdlib/wcstod.c
user compiling c file: opensource_libs/musl/src/stdlib/wcstol.c
user compiling c file: opensource_libs/musl/src/string/bcmp.c
user compiling c file: opensource_libs/musl/src/string/memcmp.c
user compiling c file: opensource_libs/musl/src/string/memchr.c
user compiling c file: opensource_libs/musl/src/string/memccpy.c
user compiling c file: opensource_libs/musl/src/string/memcpy.c
user compiling c file: opensource_libs/musl/src/string/memmem.c
user compiling c file: opensource_libs/musl/src/string/mempcpy.c
user compiling c file: opensource_libs/musl/src/string/memmove.c
user compiling c file: opensource_libs/musl/src/string/memrchr.c
user compiling c file: opensource_libs/musl/src/string/memset.c
user compiling c file: opensource_libs/musl/src/string/stpcpy.c
user compiling c file: opensource_libs/musl/src/string/stpncpy.c
user compiling c file: opensource_libs/musl/src/string/strcasecmp.c
user compiling c file: opensource_libs/musl/src/string/strcasestr.c
user compiling c file: opensource_libs/musl/src/string/strcat.c
user compiling c file: opensource_libs/musl/src/string/strchr.c
user compiling c file: opensource_libs/musl/src/string/strchrnul.c
user compiling c file: opensource_libs/musl/src/string/strcmp.c
user compiling c file: opensource_libs/musl/src/string/strcpy.c
user compiling c file: opensource_libs/musl/src/string/strcspn.c
user compiling c file: opensource_libs/musl/src/string/strdup.c
user compiling c file: opensource_libs/musl/src/string/strlen.c
user compiling c file: opensource_libs/musl/src/string/strerror_r.c
user compiling c file: opensource_libs/musl/src/string/strncasecmp.c
user compiling c file: opensource_libs/musl/src/string/strncmp.c
user compiling c file: opensource_libs/musl/src/string/strncat.c
user compiling c file: opensource_libs/musl/src/string/strncpy.c
user compiling c file: opensource_libs/musl/src/string/strnlen.c
user compiling c file: opensource_libs/musl/src/string/strpbrk.c
user compiling c file: opensource_libs/musl/src/string/strndup.c
user compiling c file: opensource_libs/musl/src/string/strsep.c
user compiling c file: opensource_libs/musl/src/string/strrchr.c
user compiling c file: opensource_libs/musl/src/string/strsignal.c
user compiling c file: opensource_libs/musl/src/string/strspn.c
user compiling c file: opensource_libs/musl/src/string/strstr.c
user compiling c file: opensource_libs/musl/src/string/strtok.c
user compiling c file: opensource_libs/musl/src/string/strtok_r.c
user compiling c file: opensource_libs/musl/src/string/swab.c
user compiling c file: opensource_libs/musl/src/string/strverscmp.c
user compiling c file: opensource_libs/musl/src/string/wcpcpy.c
user compiling c file: opensource_libs/musl/src/string/wcpncpy.c
user compiling c file: opensource_libs/musl/src/string/wcscasecmp.c
user compiling c file: opensource_libs/musl/src/string/wcscasecmp_l.c
user compiling c file: opensource_libs/musl/src/string/wcscat.c
user compiling c file: opensource_libs/musl/src/string/wcschr.c
user compiling c file: opensource_libs/musl/src/string/wcscmp.c
user compiling c file: opensource_libs/musl/src/string/wcscpy.c
user compiling c file: opensource_libs/musl/src/string/wcscspn.c
user compiling c file: opensource_libs/musl/src/string/wcsdup.c
user compiling c file: opensource_libs/musl/src/string/wcslen.c
user compiling c file: opensource_libs/musl/src/string/wcsncasecmp.c
user compiling c file: opensource_libs/musl/src/string/wcsncasecmp_l.c
user compiling c file: opensource_libs/musl/src/string/wcsncat.c
user compiling c file: opensource_libs/musl/src/string/wcsncmp.c
user compiling c file: opensource_libs/musl/src/string/wcsncpy.c
user compiling c file: opensource_libs/musl/src/string/wcsnlen.c
user compiling c file: opensource_libs/musl/src/string/wcspbrk.c
user compiling c file: opensource_libs/musl/src/string/wcsrchr.c
user compiling c file: opensource_libs/musl/src/string/wcsspn.c
user compiling c file: opensource_libs/musl/src/string/wcsstr.c
user compiling c file: opensource_libs/musl/src/string/wcstok.c
user compiling c file: opensource_libs/musl/src/string/wcswcs.c
user compiling c file: opensource_libs/musl/src/string/wmemchr.c
user compiling c file: opensource_libs/musl/src/string/wmemcmp.c
user compiling c file: opensource_libs/musl/src/string/wmemcpy.c
user compiling c file: opensource_libs/musl/src/string/wmemmove.c
user compiling c file: opensource_libs/musl/src/string/wmemset.c
user compiling c file: opensource_libs/musl/src/stdio/fputs.c
user compiling c file: opensource_libs/musl/src/stdio/fclose.c
user compiling c file: opensource_libs/musl/src/stdio/asprintf.c
user compiling c file: opensource_libs/musl/src/stdio/fflush.c
user compiling c file: opensource_libs/musl/src/stdio/fputc.c
user compiling c file: opensource_libs/musl/src/stdio/fprintf.c
user compiling c file: opensource_libs/musl/src/stdio/fread.c
user compiling c file: opensource_libs/musl/src/stdio/fileno.c
user compiling c file: opensource_libs/musl/src/stdio/fseek.c
user compiling c file: opensource_libs/musl/src/stdio/fwrite.c
user compiling c file: opensource_libs/musl/src/stdio/ftell.c
user compiling c file: opensource_libs/musl/src/stdio/getc.c
user compiling c file: opensource_libs/musl/src/stdio/ofl.c
user compiling c file: opensource_libs/musl/src/stdio/putc_unlocked.c
user compiling c file: opensource_libs/musl/src/stdio/printf.c
user compiling c file: opensource_libs/musl/src/stdio/putchar.c
user compiling c file: opensource_libs/musl/src/stdio/puts.c
user compiling c file: opensource_libs/musl/src/stdio/sscanf.c
user compiling c file: opensource_libs/musl/src/stdio/stderr.c
user compiling c file: opensource_libs/musl/src/stdio/snprintf.c
user compiling c file: opensource_libs/musl/src/stdio/sprintf.c
user compiling c file: opensource_libs/musl/src/stdio/stdin.c
user compiling c file: opensource_libs/musl/src/stdio/stdout.c
user compiling c file: opensource_libs/musl/src/stdio/ungetc.c
user compiling c file: opensource_libs/musl/src/stdio/vasprintf.c
user compiling c file: opensource_libs/musl/src/stdio/vprintf.c
user compiling c file: opensource_libs/musl/src/stdio/vfprintf.c
user compiling c file: opensource_libs/musl/src/stdio/vsnprintf.c
user compiling c file: opensource_libs/musl/src/stdio/vsprintf.c
user compiling c file: opensource_libs/musl/src/stdio/vfscanf.c
user compiling c file: opensource_libs/musl/src/stdio/__lockfile.c
user compiling c file: opensource_libs/musl/src/stdio/vsscanf.c
user compiling c file: opensource_libs/musl/src/stdio/__overflow.c
user compiling c file: opensource_libs/musl/src/stdio/__stdio_close.c
user compiling c file: opensource_libs/musl/src/stdio/__stdio_exit.c
user compiling c file: opensource_libs/musl/src/stdio/__stdio_read.c
user compiling c file: opensource_libs/musl/src/stdio/__stdio_write.c
user compiling c file: opensource_libs/musl/src/stdio/__stdio_seek.c
user compiling c file: opensource_libs/musl/src/stdio/__string_read.c
user compiling c file: opensource_libs/musl/src/stdio/__toread.c
user compiling c file: opensource_libs/musl/src/stdio/__towrite.c
user compiling c file: opensource_libs/musl/src/stdio/__uflow.c
user compiling c file: opensource_libs/musl/src/thread/__lock.c
user compiling c file: opensource_libs/musl/src/thread/__wait.c
user compiling c file: opensource_libs/musl/src/thread/pthread_once.c
user compiling c file: opensource_libs/musl/src/thread/default_attr.c
user compiling c file: opensource_libs/musl/src/thread/pthread_cleanup_push.c
user compiling c file: opensource_libs/musl/src/time/localtime.c
user compiling c file: opensource_libs/musl/src/time/gettimeofday.c
user compiling c file: opensource_libs/musl/src/time/gmtime.c
user compiling c file: opensource_libs/musl/src/time/gmtime_r.c
user compiling c file: opensource_libs/musl/src/time/localtime_r.c
user compiling c file: opensource_libs/musl/src/time/time.c
user compiling c file: opensource_libs/musl/src/time/__secs_to_tm.c
user compiling c file: opensource_libs/musl/src/unistd/sleep.c
user compiling c file: opensource_libs/musl/src/math/acos.c
user compiling c file: opensource_libs/musl/src/unistd/usleep.c
user compiling c file: opensource_libs/musl/src/math/acosf.c
user compiling c file: opensource_libs/musl/src/math/acoshl.c
user compiling c file: opensource_libs/musl/src/math/acosl.c
user compiling c file: opensource_libs/musl/src/math/acoshf.c
user compiling c file: opensource_libs/musl/src/math/acosh.c
user compiling c file: opensource_libs/musl/src/math/asin.c
user compiling c file: opensource_libs/musl/src/math/asinf.c
user compiling c file: opensource_libs/musl/src/math/asinh.c
user compiling c file: opensource_libs/musl/src/math/asinhf.c
user compiling c file: opensource_libs/musl/src/math/asinhl.c
user compiling c file: opensource_libs/musl/src/math/atan2.c
user compiling c file: opensource_libs/musl/src/math/asinl.c
user compiling c file: opensource_libs/musl/src/math/atan2f.c
user compiling c file: opensource_libs/musl/src/math/atan2l.c
user compiling c file: opensource_libs/musl/src/math/atanf.c
user compiling c file: opensource_libs/musl/src/math/atan.c
user compiling c file: opensource_libs/musl/src/math/atanh.c
user compiling c file: opensource_libs/musl/src/math/atanhf.c
user compiling c file: opensource_libs/musl/src/math/atanhl.c
user compiling c file: opensource_libs/musl/src/math/atanl.c
user compiling c file: opensource_libs/musl/src/math/cbrt.c
user compiling c file: opensource_libs/musl/src/math/cbrtf.c
user compiling c file: opensource_libs/musl/src/math/cbrtl.c
user compiling c file: opensource_libs/musl/src/math/ceil.c
user compiling c file: opensource_libs/musl/src/math/ceilf.c
user compiling c file: opensource_libs/musl/src/math/ceill.c
user compiling c file: opensource_libs/musl/src/math/copysign.c
user compiling c file: opensource_libs/musl/src/math/copysignl.c
user compiling c file: opensource_libs/musl/src/math/copysignf.c
user compiling c file: opensource_libs/musl/src/math/__cos.c
user compiling c file: opensource_libs/musl/src/math/cos.c
user compiling c file: opensource_libs/musl/src/math/__cosdf.c
user compiling c file: opensource_libs/musl/src/math/cosf.c
user compiling c file: opensource_libs/musl/src/math/cosh.c
user compiling c file: opensource_libs/musl/src/math/coshf.c
user compiling c file: opensource_libs/musl/src/math/coshl.c
user compiling c file: opensource_libs/musl/src/math/__cosl.c
user compiling c file: opensource_libs/musl/src/math/cosl.c
user compiling c file: opensource_libs/musl/src/math/erf.c
user compiling c file: opensource_libs/musl/src/math/erff.c
user compiling c file: opensource_libs/musl/src/math/erfl.c
user compiling c file: opensource_libs/musl/src/math/exp10.c
user compiling c file: opensource_libs/musl/src/math/exp10f.c
user compiling c file: opensource_libs/musl/src/math/exp10l.c
user compiling c file: opensource_libs/musl/src/math/exp2f_data.c
user compiling c file: opensource_libs/musl/src/math/exp2.c
user compiling c file: opensource_libs/musl/src/math/exp2f.c
user compiling c file: opensource_libs/musl/src/math/exp.c
user compiling c file: opensource_libs/musl/src/math/exp2l.c
user compiling c file: opensource_libs/musl/src/math/exp_data.c
user compiling c file: opensource_libs/musl/src/math/expf.c
user compiling c file: opensource_libs/musl/src/math/expl.c
user compiling c file: opensource_libs/musl/src/math/expm1.c
user compiling c file: opensource_libs/musl/src/math/expm1f.c
user compiling c file: opensource_libs/musl/src/math/expm1l.c
user compiling c file: opensource_libs/musl/src/math/__expo2.c
user compiling c file: opensource_libs/musl/src/math/__expo2f.c
user compiling c file: opensource_libs/musl/src/math/fabs.c
user compiling c file: opensource_libs/musl/src/math/fabsf.c
user compiling c file: opensource_libs/musl/src/math/fabsl.c
user compiling c file: opensource_libs/musl/src/math/fdim.c
user compiling c file: opensource_libs/musl/src/math/fdimf.c
user compiling c file: opensource_libs/musl/src/math/fdiml.c
user compiling c file: opensource_libs/musl/src/math/finitef.c
user compiling c file: opensource_libs/musl/src/math/floor.c
user compiling c file: opensource_libs/musl/src/math/finite.c
user compiling c file: opensource_libs/musl/src/math/floorf.c
user compiling c file: opensource_libs/musl/src/math/floorl.c
user compiling c file: opensource_libs/musl/src/math/fmaf.c
user compiling c file: opensource_libs/musl/src/math/fma.c
user compiling c file: opensource_libs/musl/src/math/fmal.c
user compiling c file: opensource_libs/musl/src/math/fmax.c
user compiling c file: opensource_libs/musl/src/math/fmaxf.c
user compiling c file: opensource_libs/musl/src/math/fmaxl.c
user compiling c file: opensource_libs/musl/src/math/fmin.c
user compiling c file: opensource_libs/musl/src/math/fminf.c
user compiling c file: opensource_libs/musl/src/math/fminl.c
user compiling c file: opensource_libs/musl/src/math/fmod.c
user compiling c file: opensource_libs/musl/src/math/fmodf.c
user compiling c file: opensource_libs/musl/src/math/fmodl.c
user compiling c file: opensource_libs/musl/src/math/__fpclassify.c
user compiling c file: opensource_libs/musl/src/math/__fpclassifyf.c
user compiling c file: opensource_libs/musl/src/math/__fpclassifyl.c
user compiling c file: opensource_libs/musl/src/math/frexp.c
user compiling c file: opensource_libs/musl/src/math/frexpf.c
user compiling c file: opensource_libs/musl/src/math/frexpl.c
user compiling c file: opensource_libs/musl/src/math/hypot.c
user compiling c file: opensource_libs/musl/src/math/hypotf.c
user compiling c file: opensource_libs/musl/src/math/hypotl.c
user compiling c file: opensource_libs/musl/src/math/ilogb.c
user compiling c file: opensource_libs/musl/src/math/ilogbf.c
user compiling c file: opensource_libs/musl/src/math/ilogbl.c
user compiling c file: opensource_libs/musl/src/math/__invtrigl.c
user compiling c file: opensource_libs/musl/src/math/j0.c
user compiling c file: opensource_libs/musl/src/math/j0f.c
user compiling c file: opensource_libs/musl/src/math/j1.c
user compiling c file: opensource_libs/musl/src/math/j1f.c
user compiling c file: opensource_libs/musl/src/math/jn.c
user compiling c file: opensource_libs/musl/src/math/jnf.c
user compiling c file: opensource_libs/musl/src/math/ldexp.c
user compiling c file: opensource_libs/musl/src/math/ldexpf.c
user compiling c file: opensource_libs/musl/src/math/ldexpl.c
user compiling c file: opensource_libs/musl/src/math/lgamma.c
user compiling c file: opensource_libs/musl/src/math/lgammaf.c
user compiling c file: opensource_libs/musl/src/math/lgammaf_r.c
user compiling c file: opensource_libs/musl/src/math/lgammal.c
user compiling c file: opensource_libs/musl/src/math/lgamma_r.c
user compiling c file: opensource_libs/musl/src/math/llrint.c
user compiling c file: opensource_libs/musl/src/math/llrintf.c
user compiling c file: opensource_libs/musl/src/math/llrintl.c
user compiling c file: opensource_libs/musl/src/math/llroundf.c
user compiling c file: opensource_libs/musl/src/math/llround.c
user compiling c file: opensource_libs/musl/src/math/llroundl.c
user compiling c file: opensource_libs/musl/src/math/log10.c
user compiling c file: opensource_libs/musl/src/math/log10f.c
user compiling c file: opensource_libs/musl/src/math/log1p.c
user compiling c file: opensource_libs/musl/src/math/log10l.c
user compiling c file: opensource_libs/musl/src/math/log1pf.c
user compiling c file: opensource_libs/musl/src/math/log2.c
user compiling c file: opensource_libs/musl/src/math/log1pl.c
user compiling c file: opensource_libs/musl/src/math/log2_data.c
user compiling c file: opensource_libs/musl/src/math/log2f.c
user compiling c file: opensource_libs/musl/src/math/log2f_data.c
user compiling c file: opensource_libs/musl/src/math/logb.c
user compiling c file: opensource_libs/musl/src/math/log2l.c
user compiling c file: opensource_libs/musl/src/math/logbf.c
user compiling c file: opensource_libs/musl/src/math/logbl.c
user compiling c file: opensource_libs/musl/src/math/log.c
user compiling c file: opensource_libs/musl/src/math/log_data.c
user compiling c file: opensource_libs/musl/src/math/logf.c
user compiling c file: opensource_libs/musl/src/math/logf_data.c
user compiling c file: opensource_libs/musl/src/math/logl.c
user compiling c file: opensource_libs/musl/src/math/lrintf.c
user compiling c file: opensource_libs/musl/src/math/lrint.c
user compiling c file: opensource_libs/musl/src/math/lrintl.c
user compiling c file: opensource_libs/musl/src/math/lroundf.c
user compiling c file: opensource_libs/musl/src/math/lround.c
user compiling c file: opensource_libs/musl/src/math/lroundl.c
user compiling c file: opensource_libs/musl/src/math/__math_divzero.c
user compiling c file: opensource_libs/musl/src/math/__math_divzerof.c
user compiling c file: opensource_libs/musl/src/math/__math_invalidf.c
user compiling c file: opensource_libs/musl/src/math/__math_invalid.c
user compiling c file: opensource_libs/musl/src/math/__math_oflow.c
user compiling c file: opensource_libs/musl/src/math/__math_oflowf.c
user compiling c file: opensource_libs/musl/src/math/__math_uflow.c
user compiling c file: opensource_libs/musl/src/math/__math_uflowf.c
user compiling c file: opensource_libs/musl/src/math/__math_xflow.c
user compiling c file: opensource_libs/musl/src/math/__math_xflowf.c
user compiling c file: opensource_libs/musl/src/math/modf.c
user compiling c file: opensource_libs/musl/src/math/modff.c
user compiling c file: opensource_libs/musl/src/math/modfl.c
user compiling c file: opensource_libs/musl/src/math/nan.c
user compiling c file: opensource_libs/musl/src/math/nanf.c
user compiling c file: opensource_libs/musl/src/math/nanl.c
user compiling c file: opensource_libs/musl/src/math/nearbyintf.c
user compiling c file: opensource_libs/musl/src/math/nearbyintl.c
user compiling c file: opensource_libs/musl/src/math/nearbyint.c
user compiling c file: opensource_libs/musl/src/math/nextafter.c
user compiling c file: opensource_libs/musl/src/math/nextafterl.c
user compiling c file: opensource_libs/musl/src/math/nextafterf.c
user compiling c file: opensource_libs/musl/src/math/nexttowardf.c
user compiling c file: opensource_libs/musl/src/math/nexttoward.c
user compiling c file: opensource_libs/musl/src/math/nexttowardl.c
user compiling c file: opensource_libs/musl/src/math/__polevll.c
user compiling c file: opensource_libs/musl/src/math/pow.c
user compiling c file: opensource_libs/musl/src/math/pow_data.c
user compiling c file: opensource_libs/musl/src/math/powf.c
user compiling c file: opensource_libs/musl/src/math/powl.c
user compiling c file: opensource_libs/musl/src/math/powf_data.c
user compiling c file: opensource_libs/musl/src/math/remainder.c
user compiling c file: opensource_libs/musl/src/math/remainderf.c
user compiling c file: opensource_libs/musl/src/math/remainderl.c
user compiling c file: opensource_libs/musl/src/math/__rem_pio2.c
user compiling c file: opensource_libs/musl/src/math/__rem_pio2f.c
user compiling c file: opensource_libs/musl/src/math/__rem_pio2_large.c
user compiling c file: opensource_libs/musl/src/math/__rem_pio2l.c
user compiling c file: opensource_libs/musl/src/math/remquo.c
user compiling c file: opensource_libs/musl/src/math/remquof.c
user compiling c file: opensource_libs/musl/src/math/remquol.c
user compiling c file: opensource_libs/musl/src/math/rintf.c
user compiling c file: opensource_libs/musl/src/math/rintl.c
user compiling c file: opensource_libs/musl/src/math/rint.c
user compiling c file: opensource_libs/musl/src/math/round.c
user compiling c file: opensource_libs/musl/src/math/roundf.c
user compiling c file: opensource_libs/musl/src/math/roundl.c
user compiling c file: opensource_libs/musl/src/math/scalb.c
user compiling c file: opensource_libs/musl/src/math/scalbf.c
user compiling c file: opensource_libs/musl/src/math/scalblnf.c
user compiling c file: opensource_libs/musl/src/math/scalbln.c
user compiling c file: opensource_libs/musl/src/math/scalbn.c
user compiling c file: opensource_libs/musl/src/math/scalblnl.c
user compiling c file: opensource_libs/musl/src/math/scalbnf.c
user compiling c file: opensource_libs/musl/src/math/__signbit.c
user compiling c file: opensource_libs/musl/src/math/scalbnl.c
user compiling c file: opensource_libs/musl/src/math/__signbitf.c
user compiling c file: opensource_libs/musl/src/math/__signbitl.c
user compiling c file: opensource_libs/musl/src/math/signgam.c
user compiling c file: opensource_libs/musl/src/math/significand.c
user compiling c file: opensource_libs/musl/src/math/significandf.c
user compiling c file: opensource_libs/musl/src/math/__sin.c
user compiling c file: opensource_libs/musl/src/math/sin.c
user compiling c file: opensource_libs/musl/src/math/sincos.c
user compiling c file: opensource_libs/musl/src/math/sincosf.c
user compiling c file: opensource_libs/musl/src/math/sincosl.c
user compiling c file: opensource_libs/musl/src/math/__sindf.c
user compiling c file: opensource_libs/musl/src/math/sinh.c
user compiling c file: opensource_libs/musl/src/math/sinf.c
user compiling c file: opensource_libs/musl/src/math/sinhf.c
user compiling c file: opensource_libs/musl/src/math/sinhl.c
user compiling c file: opensource_libs/musl/src/math/__sinl.c
user compiling c file: opensource_libs/musl/src/math/sinl.c
user compiling c file: opensource_libs/musl/src/math/sqrt.c
user compiling c file: opensource_libs/musl/src/math/sqrtf.c
user compiling c file: opensource_libs/musl/src/math/sqrtl.c
user compiling c file: opensource_libs/musl/src/math/__tan.c
user compiling c file: opensource_libs/musl/src/math/tan.c
user compiling c file: opensource_libs/musl/src/math/__tandf.c
user compiling c file: opensource_libs/musl/src/math/tanf.c
user compiling c file: opensource_libs/musl/src/math/tanh.c
user compiling c file: opensource_libs/musl/src/math/tanhf.c
user compiling c file: opensource_libs/musl/src/math/tanhl.c
user compiling c file: opensource_libs/musl/src/math/__tanl.c
user compiling c file: opensource_libs/musl/src/math/tanl.c
user compiling c file: opensource_libs/musl/src/math/tgamma.c
user compiling c file: opensource_libs/musl/src/math/tgammaf.c
user compiling c file: opensource_libs/musl/src/math/tgammal.c
user compiling c file: opensource_libs/musl/src/math/truncf.c
user compiling c file: opensource_libs/musl/src/math/trunc.c
user compiling c file: opensource_libs/musl/src/math/truncl.c
user compiling c file: opensource_libs/musl/src/locale/bind_textdomain_codeset.c
user compiling c file: opensource_libs/musl/src/locale/catgets.c
user compiling c file: opensource_libs/musl/src/locale/catclose.c
user compiling c file: opensource_libs/musl/src/locale/iconv_close.c
user compiling c file: opensource_libs/musl/src/locale/langinfo.c
user compiling c file: opensource_libs/musl/src/locale/__lctrans.c
user compiling c file: opensource_libs/musl/src/locale/catopen.c
user compiling c file: opensource_libs/musl/src/locale/c_locale.c
user compiling c file: opensource_libs/musl/src/locale/iconv.c
user compiling c file: opensource_libs/musl/src/locale/localeconv.c
user compiling c file: opensource_libs/musl/src/locale/pleval.c
user compiling c file: opensource_libs/musl/src/locale/__mo_lookup.c
user compiling c file: opensource_libs/musl/src/locale/strcoll.c
user compiling c file: opensource_libs/musl/src/locale/strfmon.c
user compiling c file: opensource_libs/musl/src/locale/strxfrm.c
user compiling c file: opensource_libs/musl/src/locale/textdomain.c
user compiling c file: opensource_libs/musl/src/locale/wcsxfrm.c
user compiling c file: opensource_libs/musl/src/locale/wcscoll.c
user compiling c file: user/base/lib/libc-rctee/pthreads.c
creating static lib: out/build-imx8mp/user_product/lib/libc-ext/liblibc-ext.a
creating static lib: out/build-imx8mp/user_product/lib/libc-main/liblibc-main.a
creating static lib: out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a
creating static lib: out/build-imx8mp/user_product/lib/storage/libstorage.a
creating static lib: out/build-imx8mp/user_product/lib/system_state/libsystem_state.a
user compiling asm file: out/build-imx8mp/generated/user/base/lib/syscall-stubs/rctee_syscalls.S
user compiling c file: kernel/hardware/nxp/app/hwcrypto/main.c
user compiling c file: kernel/hardware/nxp/app/hwcrypto/hwkey_srv.c
user compiling c file: kernel/hardware/nxp/app/hwcrypto/hwcrypto_srv.c
user compiling c file: kernel/hardware/nxp/app/hwcrypto/hwkey_srv_provider.c
user compiling c file: kernel/hardware/nxp/app/hwcrypto/caam.c
user compiling c file: kernel/hardware/nxp/app/hwcrypto/hwcrypto_srv_provider.c
user compiling c file: kernel/hardware/nxp/app/hwcrypto/hwaes_srv.c
user compiling c file: kernel/hardware/nxp/app/hwcrypto/hwaes_srv_provider.c
creating static lib: out/build-imx8mp/user_product/lib/srv/libsrv.a
creating static lib: out/build-imx8mp/user_product/lib/Tongsuo/libTongsuo.a
user compiling c file: user/app/sample/memref-test/receiver/receiver.c
creating out/build-imx8mp/kernel/hardware/nxp/platform/imx.mod.a
creating out/build-imx8mp/kernel/lk/app.mod.a
user compiling c file: user/app/sample/manifest-test/manifest_test.c
user compiling c file: user/app/test/tongsuo_test/main.c
creating out/build-imx8mp/kernel/lk/arch/arm64.mod.a
creating out/build-imx8mp/kernel/lk/dev/interrupt/arm_gic.mod.a
creating out/build-imx8mp/kernel/lk/dev.mod.a
creating out/build-imx8mp/kernel/lk/dev/timer/arm_generic.mod.a
creating out/build-imx8mp/kernel/lk/kernel.mod.a
creating out/build-imx8mp/kernel/lk/kernel/vm.mod.a
creating out/build-imx8mp/kernel/lk/lib/binary_search_tree.mod.a
creating out/build-imx8mp/kernel/lk/lib/cbuf.mod.a
creating out/build-imx8mp/kernel/lk/lib/debug.mod.a
creating out/build-imx8mp/kernel/lk/lib/fixed_point.mod.a
creating out/build-imx8mp/kernel/lk/lib/heap.mod.a
creating out/build-imx8mp/kernel/lk/lib/io.mod.a
creating out/build-imx8mp/kernel/lk/lib/heap/miniheap.mod.a
creating out/build-imx8mp/kernel/lk/platform.mod.a
creating out/build-imx8mp/kernel/lk/target.mod.a
creating out/build-imx8mp/kernel/lk/top.mod.a
creating out/build-imx8mp/kernel/rctee/app/busytest.mod.a
creating out/build-imx8mp/kernel/rctee/lib/app_manifest.mod.a
creating out/build-imx8mp/kernel/rctee/lib/arm_ffa.mod.a
creating out/build-imx8mp/kernel/rctee/lib/backtrace.mod.a
creating out/build-imx8mp/kernel/rctee/lib/extmem.mod.a
creating out/build-imx8mp/kernel/rctee/lib/ktipc.mod.a
creating out/build-imx8mp/kernel/rctee/lib/ktipc/test/main.mod.a
creating out/build-imx8mp/kernel/rctee/lib/ktipc/test/srv.mod.a
creating out/build-imx8mp/kernel/rctee/lib/libc-ext.mod.a
creating out/build-imx8mp/kernel/rctee/lib/libc-trusty.mod.a
creating out/build-imx8mp/kernel/rctee/lib/memlog.mod.a
user/app/test/tongsuo_test/main.c:40:2: warning: "Tongsuo-only TA: BoringSSL excluded, using Tongsuo for all algorithms" [-W#warnings]
#warning "Tongsuo-only TA: BoringSSL excluded, using Tongsuo for all algorithms"
 ^
creating out/build-imx8mp/kernel/rctee/lib/rand.mod.a
creating out/build-imx8mp/kernel/rctee/lib/rctee.mod.a
creating out/build-imx8mp/kernel/rctee/lib/sm.mod.a
creating out/build-imx8mp/kernel/rctee/lib/smc.mod.a
creating out/build-imx8mp/kernel/rctee/lib/syscall.mod.a
creating out/build-imx8mp/kernel/rctee/lib/ubsan.mod.a
creating out/build-imx8mp/kernel/rctee/lib/unittest.mod.a
creating out/build-imx8mp/kernel/rctee/services/apploader.mod.a
creating out/build-imx8mp/kernel/rctee/services/generic_ta_service.mod.a
creating out/build-imx8mp/kernel/rctee/services/hwrng.mod.a
creating out/build-imx8mp/kernel/rctee/services/smc.mod.a
creating static lib: out/build-imx8mp/user_product/lib/boringssl/libboringssl.a
creating static lib: out/build-imx8mp/user_product/lib/hwaes/libhwaes.a
creating static lib: out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a
1 warning generated.
creating static lib: out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a
creating static lib: out/build-imx8mp/user_product/lib/open-dice/libopen-dice.a
creating static lib: out/build-imx8mp/user_product/lib/common/libcommon.a
creating static lib: out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a
creating static lib: out/build-imx8mp/user_product/lib/apploader_package/libapploader_package.a
creating static lib: out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a
creating static lib: out/build-imx8mp/user_product/lib/unittest/libunittest.a
creating static lib: out/build-imx8mp/user_product/lib/apploader_policy_engine/libapploader_policy_engine.a
generate TA_SYMS_ELF: out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.syms.elf
generate TA_SYMS_ELF: out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.syms.elf
TA_LD_FIRST_OBJ=out/build-imx8mp/user_product/lib/libc-main/opensource_libs/musl/crt/rcrt1.o TA_LINK_BINS= out/build-imx8mp/user_product/lib/Tongsuo/libTongsuo.a out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a out/build-imx8mp/user_product/lib/rng/librng.a out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a out/build-imx8mp/user_product/lib/tipc/libtipc.a
/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/ld.lld  -z max-page-size=4096 -z separate-loadable-segments --undefined=__aeabi_unwind_cpp_pr0 --gc-sections -static -pie --no-dynamic-linker -z text -Bsymbolic  -Lout/build-imx8mp/user_product/lib/Tongsuo  -Lout/build-imx8mp/user_product/lib/dlmalloc  -Lout/build-imx8mp/user_product/lib/libc-rctee  -Lout/build-imx8mp/user_product/lib/libcxxabi-trusty  -Lout/build-imx8mp/user_product/lib/libstdc++-trusty  -Lout/build-imx8mp/user_product/lib/line-coverage  -Lout/build-imx8mp/user_product/lib/rng  -Lout/build-imx8mp/user_product/lib/syscall-stubs  -Lout/build-imx8mp/user_product/lib/tipc  -lTongsuo  -ldlmalloc  -llibc-rctee  -llibcxxabi-trusty  -llibstdc++-trusty  -lline-coverage  -lrng  -lsyscall-stubs  -ltipc  --start-group out/build-imx8mp/user_product/lib/libc-main/opensource_libs/musl/crt/rcrt1.o     out/build-imx8mp/user_product/ta/tongsuo_test/user/app/test/tongsuo_test/main.o      out/build-imx8mp/user_product/lib/Tongsuo/libTongsuo.a  out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a  out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a  out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a  out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a  out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a  out/build-imx8mp/user_product/lib/rng/librng.a  out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a  out/build-imx8mp/user_product/lib/tipc/libtipc.a /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/../runtimes_ndk_cxx/libclang_rt.builtins-aarch64-android.a  --end-group -o out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.syms.elf
TA_LD_FIRST_OBJ=out/build-imx8mp/user_product/lib/libc-main/opensource_libs/musl/crt/rcrt1.o TA_LINK_BINS= out/build-imx8mp/user_product/lib/libc-ext/liblibc-ext.a out/build-imx8mp/user_product/lib/boringssl/libboringssl.a out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a out/build-imx8mp/user_product/lib/srv/libsrv.a out/build-imx8mp/user_product/lib/hwkey/libhwkey.a out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a out/build-imx8mp/user_product/lib/rng/librng.a out/build-imx8mp/user_product/lib/storage/libstorage.a out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a out/build-imx8mp/user_product/lib/tipc/libtipc.a
/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/ld.lld  -z max-page-size=4096 -z separate-loadable-segments --undefined=__aeabi_unwind_cpp_pr0 --gc-sections -static -pie --no-dynamic-linker -z text -Bsymbolic  -Lout/build-imx8mp/user_product/lib/boringssl  -Lout/build-imx8mp/user_product/lib/dlmalloc  -Lout/build-imx8mp/user_product/lib/hwkey  -Lout/build-imx8mp/user_product/lib/libc-ext  -Lout/build-imx8mp/user_product/lib/libc-rctee  -Lout/build-imx8mp/user_product/lib/libcxxabi-trusty  -Lout/build-imx8mp/user_product/lib/libstdc++-trusty  -Lout/build-imx8mp/user_product/lib/line-coverage  -Lout/build-imx8mp/user_product/lib/rng  -Lout/build-imx8mp/user_product/lib/srv  -Lout/build-imx8mp/user_product/lib/storage  -Lout/build-imx8mp/user_product/lib/syscall-stubs  -Lout/build-imx8mp/user_product/lib/tipc  -lboringssl  -ldlmalloc  -lhwkey  -llibc-ext  -llibc-rctee  -llibcxxabi-trusty  -llibstdc++-trusty  -lline-coverage  -lrng  -lsrv  -lstorage  -lsyscall-stubs  -ltipc  --start-group out/build-imx8mp/user_product/lib/libc-main/opensource_libs/musl/crt/rcrt1.o     out/build-imx8mp/user_product/ta/hwcrypto/kernel/hardware/nxp/app/hwcrypto/main.o out/build-imx8mp/user_product/ta/hwcrypto/kernel/hardware/nxp/app/hwcrypto/hwkey_srv.o out/build-imx8mp/user_product/ta/hwcrypto/kernel/hardware/nxp/app/hwcrypto/hwcrypto_srv.o out/build-imx8mp/user_product/ta/hwcrypto/kernel/hardware/nxp/app/hwcrypto/hwkey_srv_provider.o out/build-imx8mp/user_product/ta/hwcrypto/kernel/hardware/nxp/app/hwcrypto/hwcrypto_srv_provider.o out/build-imx8mp/user_product/ta/hwcrypto/kernel/hardware/nxp/app/hwcrypto/caam.o out/build-imx8mp/user_product/ta/hwcrypto/kernel/hardware/nxp/app/hwcrypto/hwaes_srv.o out/build-imx8mp/user_product/ta/hwcrypto/kernel/hardware/nxp/app/hwcrypto/hwaes_srv_provider.o      out/build-imx8mp/user_product/lib/libc-ext/liblibc-ext.a  out/build-imx8mp/user_product/lib/boringssl/libboringssl.a  out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a  out/build-imx8mp/user_product/lib/srv/libsrv.a  out/build-imx8mp/user_product/lib/hwkey/libhwkey.a  out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a  out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a  out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a  out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a  out/build-imx8mp/user_product/lib/rng/librng.a  out/build-imx8mp/user_product/lib/storage/libstorage.a  out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a  out/build-imx8mp/user_product/lib/tipc/libtipc.a /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/../runtimes_ndk_cxx/libclang_rt.builtins-aarch64-android.a  --end-group -o out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.syms.elf
generate TA_SYMS_ELF: out/build-imx8mp/user_product/ta/receiver/receiver.syms.elf
TA_LD_FIRST_OBJ=out/build-imx8mp/user_product/lib/libc-main/opensource_libs/musl/crt/rcrt1.o TA_LINK_BINS= out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a out/build-imx8mp/user_product/lib/rng/librng.a out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a out/build-imx8mp/user_product/lib/tipc/libtipc.a out/build-imx8mp/user_product/lib/unittest/libunittest.a
generate TA_SYMS_ELF: out/build-imx8mp/user_product/ta/manifest-test/manifest-test.syms.elf
/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/ld.lld  -z max-page-size=4096 -z separate-loadable-segments --undefined=__aeabi_unwind_cpp_pr0 --gc-sections -static -pie --no-dynamic-linker -z text -Bsymbolic  -Lout/build-imx8mp/user_product/lib/dlmalloc  -Lout/build-imx8mp/user_product/lib/libc-rctee  -Lout/build-imx8mp/user_product/lib/libcxxabi-trusty  -Lout/build-imx8mp/user_product/lib/libstdc++-trusty  -Lout/build-imx8mp/user_product/lib/line-coverage  -Lout/build-imx8mp/user_product/lib/rng  -Lout/build-imx8mp/user_product/lib/syscall-stubs  -Lout/build-imx8mp/user_product/lib/tipc  -Lout/build-imx8mp/user_product/lib/unittest  -ldlmalloc  -llibc-rctee  -llibcxxabi-trusty  -llibstdc++-trusty  -lline-coverage  -lrng  -lsyscall-stubs  -ltipc  -lunittest  --start-group out/build-imx8mp/user_product/lib/libc-main/opensource_libs/musl/crt/rcrt1.o     out/build-imx8mp/user_product/ta/receiver/user/app/sample/memref-test/receiver/receiver.o      out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a  out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a  out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a  out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a  out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a  out/build-imx8mp/user_product/lib/rng/librng.a  out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a  out/build-imx8mp/user_product/lib/tipc/libtipc.a  out/build-imx8mp/user_product/lib/unittest/libunittest.a /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/../runtimes_ndk_cxx/libclang_rt.builtins-aarch64-android.a  --end-group -o out/build-imx8mp/user_product/ta/receiver/receiver.syms.elf
TA_LD_FIRST_OBJ=out/build-imx8mp/user_product/lib/libc-main/opensource_libs/musl/crt/rcrt1.o TA_LINK_BINS= out/build-imx8mp/user_product/lib/apploader_package/libapploader_package.a out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a out/build-imx8mp/user_product/lib/rng/librng.a out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a out/build-imx8mp/user_product/lib/tipc/libtipc.a out/build-imx8mp/user_product/lib/unittest/libunittest.a
/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/ld.lld  -z max-page-size=4096 -z separate-loadable-segments --undefined=__aeabi_unwind_cpp_pr0 --gc-sections -static -pie --no-dynamic-linker -z text -Bsymbolic  -Lout/build-imx8mp/user_product/lib/apploader_package  -Lout/build-imx8mp/user_product/lib/dlmalloc  -Lout/build-imx8mp/user_product/lib/libc-rctee  -Lout/build-imx8mp/user_product/lib/libcxxabi-trusty  -Lout/build-imx8mp/user_product/lib/libstdc++-trusty  -Lout/build-imx8mp/user_product/lib/line-coverage  -Lout/build-imx8mp/user_product/lib/rng  -Lout/build-imx8mp/user_product/lib/syscall-stubs  -Lout/build-imx8mp/user_product/lib/tipc  -Lout/build-imx8mp/user_product/lib/unittest  -lapploader_package  -ldlmalloc  -llibc-rctee  -llibcxxabi-trusty  -llibstdc++-trusty  -lline-coverage  -lrng  -lsyscall-stubs  -ltipc  -lunittest  --start-group out/build-imx8mp/user_product/lib/libc-main/opensource_libs/musl/crt/rcrt1.o     out/build-imx8mp/user_product/ta/manifest-test/user/app/sample/manifest-test/manifest_test.o      out/build-imx8mp/user_product/lib/apploader_package/libapploader_package.a  out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a  out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a  out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a  out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a  out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a  out/build-imx8mp/user_product/lib/rng/librng.a  out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a  out/build-imx8mp/user_product/lib/tipc/libtipc.a  out/build-imx8mp/user_product/lib/unittest/libunittest.a /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/../runtimes_ndk_cxx/libclang_rt.builtins-aarch64-android.a  --end-group -o out/build-imx8mp/user_product/ta/manifest-test/manifest-test.syms.elf
generate TA_SYMS_ELF: out/build-imx8mp/user_product/ta/apploader/apploader.syms.elf
TA_LD_FIRST_OBJ=out/build-imx8mp/user_product/lib/libc-main/opensource_libs/musl/crt/rcrt1.o TA_LINK_BINS= out/build-imx8mp/user_product/lib/app_manifest/libapp_manifest.a out/build-imx8mp/user_product/lib/boringssl/libboringssl.a out/build-imx8mp/user_product/lib/open-dice/libopen-dice.a out/build-imx8mp/user_product/lib/apploader_package/libapploader_package.a out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a out/build-imx8mp/user_product/lib/hwaes/libhwaes.a out/build-imx8mp/user_product/lib/hwkey/libhwkey.a out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a out/build-imx8mp/user_product/lib/rng/librng.a out/build-imx8mp/user_product/lib/apploader_policy_engine/libapploader_policy_engine.a out/build-imx8mp/user_product/lib/storage/libstorage.a out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a out/build-imx8mp/user_product/lib/system_state/libsystem_state.a out/build-imx8mp/user_product/lib/tipc/libtipc.a
/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/ld.lld  -z max-page-size=4096 -z separate-loadable-segments --undefined=__aeabi_unwind_cpp_pr0 --gc-sections -static -pie --no-dynamic-linker -z text -Bsymbolic  -Lout/build-imx8mp/user_product/lib/app_manifest  -Lout/build-imx8mp/user_product/lib/apploader_package  -Lout/build-imx8mp/user_product/lib/apploader_policy_engine  -Lout/build-imx8mp/user_product/lib/boringssl  -Lout/build-imx8mp/user_product/lib/dlmalloc  -Lout/build-imx8mp/user_product/lib/hwaes  -Lout/build-imx8mp/user_product/lib/hwkey  -Lout/build-imx8mp/user_product/lib/libc-rctee  -Lout/build-imx8mp/user_product/lib/libcxxabi-trusty  -Lout/build-imx8mp/user_product/lib/libstdc++-trusty  -Lout/build-imx8mp/user_product/lib/line-coverage  -Lout/build-imx8mp/user_product/lib/open-dice  -Lout/build-imx8mp/user_product/lib/rng  -Lout/build-imx8mp/user_product/lib/storage  -Lout/build-imx8mp/user_product/lib/syscall-stubs  -Lout/build-imx8mp/user_product/lib/system_state  -Lout/build-imx8mp/user_product/lib/tipc  -lapp_manifest  -lapploader_package  -lapploader_policy_engine  -lboringssl  -ldlmalloc  -lhwaes  -lhwkey  -llibc-rctee  -llibcxxabi-trusty  -llibstdc++-trusty  -lline-coverage  -lopen-dice  -lrng  -lstorage  -lsyscall-stubs  -lsystem_state  -ltipc  --start-group out/build-imx8mp/user_product/lib/libc-main/opensource_libs/musl/crt/rcrt1.o     out/build-imx8mp/user_product/ta/apploader/user/base/app/apploader/apploader.o out/build-imx8mp/user_product/ta/apploader/user/base/app/apploader/app_version.o out/build-imx8mp/user_product/ta/apploader/user/base/app/apploader/app_manifest_parser.o     out/build-imx8mp/user_product/lib/app_manifest/libapp_manifest.a  out/build-imx8mp/user_product/lib/boringssl/libboringssl.a  out/build-imx8mp/user_product/lib/open-dice/libopen-dice.a  out/build-imx8mp/user_product/lib/apploader_package/libapploader_package.a  out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a  out/build-imx8mp/user_product/lib/hwaes/libhwaes.a  out/build-imx8mp/user_product/lib/hwkey/libhwkey.a  out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a  out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a  out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a  out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a  out/build-imx8mp/user_product/lib/rng/librng.a  out/build-imx8mp/user_product/lib/apploader_policy_engine/libapploader_policy_engine.a  out/build-imx8mp/user_product/lib/storage/libstorage.a  out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a  out/build-imx8mp/user_product/lib/system_state/libsystem_state.a  out/build-imx8mp/user_product/lib/tipc/libtipc.a /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/../runtimes_ndk_cxx/libclang_rt.builtins-aarch64-android.a  --end-group -o out/build-imx8mp/user_product/ta/apploader/apploader.syms.elf
stripping TA_ELF: out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.syms.elf
page aligning TA: out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.syms.elf
Packing bundled TA as kernel module: out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.elf to out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.ko
stripping TA_ELF: out/build-imx8mp/user_product/ta/manifest-test/manifest-test.syms.elf
page aligning TA: out/build-imx8mp/user_product/ta/manifest-test/manifest-test.syms.elf
Packing bundled TA as kernel module: out/build-imx8mp/user_product/ta/manifest-test/manifest-test.elf to out/build-imx8mp/user_product/ta/manifest-test/manifest-test.ko
stripping TA_ELF: out/build-imx8mp/user_product/ta/receiver/receiver.syms.elf
page aligning TA: out/build-imx8mp/user_product/ta/receiver/receiver.syms.elf
Packing bundled TA as kernel module: out/build-imx8mp/user_product/ta/receiver/receiver.elf to out/build-imx8mp/user_product/ta/receiver/receiver.ko
stripping TA_ELF: out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.syms.elf
page aligning TA: out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.syms.elf
Packing bundled TA as kernel module: out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.elf to out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.ko
stripping TA_ELF: out/build-imx8mp/user_product/ta/apploader/apploader.syms.elf
page aligning TA: out/build-imx8mp/user_product/ta/apploader/apploader.syms.elf
Packing bundled TA as kernel module: out/build-imx8mp/user_product/ta/apploader/apploader.elf to out/build-imx8mp/user_product/ta/apploader/apploader.ko
compiling kernel/rctee/lib/version/version.c
creating out/build-imx8mp/kernel/rctee/lib/version.mod.a
linking out/build-imx8mp/lk.elf
extra_objs: out/build-imx8mp/user_product/ta/apploader/apploader.ko out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.ko out/build-imx8mp/user_product/ta/receiver/receiver.ko out/build-imx8mp/user_product/ta/manifest-test/manifest-test.ko out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.ko
generating image: out/build-imx8mp/lk.bin
generating sorted symbols: out/build-imx8mp/lk.elf.sym.sorted
generating symbols: out/build-imx8mp/lk.elf.sym
generating size map: out/build-imx8mp/lk.elf.size
generating objdump: out/build-imx8mp/lk.elf.dump
generating out/build-imx8mp/include_paths.txt
generating out/build-imx8mp/srcfiles.txt
   text	   data	    bss	    dec	    hex	filename
1397393	   8472	  49424	1455289	 1634b9	out/build-imx8mp/lk.elf
building out/build-imx8mp/host_tests/obj/cbor_test/opensource_libs/open-dice/src/cbor_reader.o
building out/build-imx8mp/host_tests/obj/cbor_test/opensource_libs/open-dice/src/cbor_writer.o
building out/build-imx8mp/host_tests/obj/cbor_test/opensource_libs/googletest/googletest/src/gtest-all.o
building out/build-imx8mp/host_tests/obj/cbor_test/opensource_libs/googletest/googletest/src/gtest_main.o
building out/build-imx8mp/host_tests/obj/cbor_test/user/base/app/apploader/tests/cbor_test/cbor_test.o
building out/build-imx8mp/host_tests/obj/cbor_test/opensource_libs/libcppbor/src/cppbor.o
building out/build-imx8mp/host_tests/obj/cbor_test/opensource_libs/libcppbor/src/cppbor_parse.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/err_data.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_bitstr.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_bool.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_d2i_fp.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_dup.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_gentm.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_i2d_fp.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_int.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_mbstr.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_object.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_octet.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_strex.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_strnid.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_time.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_type.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/a_utctm.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/asn1_lib.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/asn1_par.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/asn_pack.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/f_int.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/f_string.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/posix_time.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/tasn_dec.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/tasn_enc.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/tasn_fre.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/tasn_new.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/tasn_typ.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/asn1/tasn_utl.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/base64/base64.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bio/bio_mem.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bio/bio.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bio/connect.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bio/errno.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bio/fd.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bio/hexdump.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bio/file.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bio/pair.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bio/printf.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bio/socket.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bio/socket_helper.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/blake2/blake2.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bn_extra/bn_asn1.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bn_extra/convert.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/buf/buf.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bytestring/asn1_compat.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bytestring/ber.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bytestring/cbb.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bytestring/cbs.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/bytestring/unicode.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/chacha/chacha.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cipher_extra/cipher_extra.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cipher_extra/derive_key.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_aesctrhmac.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_aesgcmsiv.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_chacha20poly1305.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_des.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_null.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_rc2.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_rc4.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cipher_extra/e_tls.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cipher_extra/tls_cbc.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/conf/conf.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cpu_aarch64_apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cpu_aarch64_fuchsia.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cpu_aarch64_linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cpu_aarch64_openbsd.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cpu_aarch64_sysreg.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cpu_arm_freebsd.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cpu_aarch64_win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cpu_arm_linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/cpu_intel.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/crypto.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/curve25519/curve25519.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/curve25519/curve25519_64_adx.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/curve25519/spake25519.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/des/des.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/dh_extra/dh_asn1.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/dh_extra/params.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/digest_extra/digest_extra.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/dsa/dsa.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/dsa/dsa_asn1.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/ec_extra/ec_asn1.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/ec_extra/ec_derive.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/ec_extra/hash_to_curve.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/ecdh_extra/ecdh_extra.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/ecdsa_extra/ecdsa_asn1.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/engine/engine.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/err/err.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/evp.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/evp_asn1.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/evp_ctx.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/p_dsa_asn1.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/p_ec.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/p_ec_asn1.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/p_ed25519.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/p_ed25519_asn1.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/p_hkdf.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/p_rsa.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/p_rsa_asn1.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/p_x25519.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/p_x25519_asn1.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/pbkdf.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/print.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/scrypt.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/evp/sign.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/ex_data.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/fipsmodule/bcm.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/fipsmodule/fips_shared_support.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/hpke/hpke.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/hrss/hrss.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/kyber/keccak.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/kyber/kyber.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/lhash/lhash.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/mem.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/obj/obj.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/obj/obj_xref.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pem/pem_all.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pem/pem_info.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pem/pem_lib.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pem/pem_oth.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pem/pem_pk8.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pem/pem_pkey.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pem/pem_x509.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pem/pem_xaux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pkcs7/pkcs7.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pkcs7/pkcs7_x509.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pkcs8/p5_pbev2.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pkcs8/pkcs8.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pkcs8/pkcs8_x509.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/poly1305/poly1305.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/poly1305/poly1305_arm.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/poly1305/poly1305_vec.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/pool/pool.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/rand_extra/deterministic.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/rand_extra/forkunsafe.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/rand_extra/getentropy.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/rand_extra/ios.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/rand_extra/passive.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/rand_extra/rand_extra.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/rand_extra/trusty.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/rand_extra/windows.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/rc4/rc4.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/refcount.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/rsa_extra/rsa_asn1.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/rsa_extra/rsa_crypt.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/rsa_extra/rsa_print.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/siphash/siphash.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/stack/stack.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/thread.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/thread_none.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/thread_pthread.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/thread_win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/trust_token/pmbtoken.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/trust_token/trust_token.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/trust_token/voprf.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/a_digest.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/a_sign.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/a_verify.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/algorithm.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/asn1_gen.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/by_dir.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/by_file.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/i2d_pr.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/name_print.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/policy.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/rsa_pss.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/t_crl.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/t_req.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/t_x509.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/t_x509a.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_att.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_cmp.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_d2.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_def.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_ext.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_lu.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_obj.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_req.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_set.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_trs.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_txt.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_v3.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_vfy.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509_vpm.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509cset.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509name.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509rset.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x509spki.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_algor.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_all.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_attrib.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_crl.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_exten.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_info.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_name.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_pkey.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_pubkey.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_req.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_sig.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_spki.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_val.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_x509.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509/x_x509a.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_akey.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_akeya.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_alt.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_bcons.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_bitst.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_conf.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_cpols.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_crld.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_enum.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_extku.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_genn.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_ia5.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_info.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_int.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_lib.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_ncons.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_ocsp.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_pcons.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_pmaps.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_prn.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_purp.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_skey.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/x509v3/v3_utl.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/chacha/chacha-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/cipher_extra/chacha20_poly1305_armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/aesv8-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/aesv8-gcm-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/armv8-mont-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/bn-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/ghash-neon-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/ghashv8-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/p256-armv8-asm-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/p256_beeu-armv8-asm-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/sha1-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/sha256-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/sha512-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/vpaes-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-aarch64/crypto/test/trampoline-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-arm/crypto/chacha/chacha-armv4-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/aesv8-armv7-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/armv4-mont-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/bsaes-armv7-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/ghash-armv4-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/ghashv8-armv7-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/sha1-armv4-large-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/sha256-armv4-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/sha512-armv4-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/vpaes-armv7-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-arm/crypto/test/trampoline-armv4-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86/crypto/chacha/chacha-x86-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/aesni-x86-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/bn-586-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/co-586-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/ghash-ssse3-x86-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/ghash-x86-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/md5-586-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/sha1-586-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/sha256-586-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/sha512-586-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/vpaes-x86-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/x86-mont-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86/crypto/test/trampoline-x86-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/chacha/chacha-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/cipher_extra/aes128gcmsiv-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/cipher_extra/chacha20_poly1305_x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/aesni-gcm-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/ghash-ssse3-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/ghash-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/md5-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/aesni-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/p256-x86_64-asm-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/p256_beeu-x86_64-asm-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/rdrand-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/rsaz-avx2-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/sha1-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/sha256-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/sha512-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/vpaes-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/x86_64-mont-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/x86_64-mont5-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/apple-x86_64/crypto/test/trampoline-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/chacha/chacha-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/cipher_extra/chacha20_poly1305_armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/aesv8-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/aesv8-gcm-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/armv8-mont-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/bn-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/ghash-neon-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/ghashv8-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/p256-armv8-asm-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/sha1-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/p256_beeu-armv8-asm-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/sha256-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/sha512-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/vpaes-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-aarch64/crypto/test/trampoline-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-arm/crypto/chacha/chacha-armv4-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/aesv8-armv7-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/armv4-mont-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/bsaes-armv7-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/ghash-armv4-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/ghashv8-armv7-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/sha1-armv4-large-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/sha256-armv4-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/sha512-armv4-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/vpaes-armv7-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-arm/crypto/test/trampoline-armv4-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86/crypto/chacha/chacha-x86-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/aesni-x86-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/bn-586-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/co-586-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/ghash-ssse3-x86-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/ghash-x86-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/md5-586-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/sha1-586-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/sha256-586-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/sha512-586-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/vpaes-x86-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/x86-mont-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86/crypto/test/trampoline-x86-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/chacha/chacha-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/cipher_extra/aes128gcmsiv-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/cipher_extra/chacha20_poly1305_x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/aesni-gcm-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/aesni-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/ghash-ssse3-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/ghash-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/md5-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/p256-x86_64-asm-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/p256_beeu-x86_64-asm-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/rdrand-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/rsaz-avx2-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/sha1-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/sha256-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/sha512-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/vpaes-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/x86_64-mont-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/x86_64-mont5-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/linux-x86_64/crypto/test/trampoline-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/hrss/asm/poly_rq_mul.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/curve25519/asm/x25519-asm-arm.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/crypto/poly1305/poly1305_arm_asm.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/third_party/fiat/asm/fiat_curve25519_adx_mul.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/src/third_party/fiat/asm/fiat_curve25519_adx_square.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/chacha/chacha-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/cipher_extra/chacha20_poly1305_armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/aesv8-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/aesv8-gcm-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/armv8-mont-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/bn-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/ghash-neon-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/ghashv8-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/p256-armv8-asm-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/p256_beeu-armv8-asm-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/sha1-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/sha256-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/sha512-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/vpaes-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl/opensource_libs/boringssl/win-aarch64/crypto/test/trampoline-armv8-win.o
aring out/build-imx8mp/host_libs/libssl.a
linking out/build-imx8mp/host_tests/cbor_test
building out/build-imx8mp/host_tools/obj/apploader_package_tool/opensource_libs/open-dice/src/cbor_reader.o
building out/build-imx8mp/host_tools/obj/apploader_package_tool/opensource_libs/open-dice/src/cbor_writer.o
building out/build-imx8mp/host_tools/obj/apploader_package_tool/build/tools/package_tool/apploader_package_tool.o
building out/build-imx8mp/host_tools/obj/apploader_package_tool/user/base/app/apploader/app_manifest_parser.o
building out/build-imx8mp/host_tools/obj/apploader_package_tool/user/base/lib/apploader_package/cose.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/err_data.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_bitstr.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_bool.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_d2i_fp.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_dup.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_gentm.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_i2d_fp.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_int.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_mbstr.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_object.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_octet.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_strex.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_strnid.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_time.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_type.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/a_utctm.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/asn1_lib.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/asn1_par.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/asn_pack.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/f_int.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/f_string.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/posix_time.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/tasn_dec.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/tasn_enc.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/tasn_fre.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/tasn_new.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/tasn_typ.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/asn1/tasn_utl.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/base64/base64.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bio/bio.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bio/bio_mem.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bio/connect.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bio/errno.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bio/fd.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bio/file.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bio/hexdump.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bio/pair.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bio/printf.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bio/socket.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bio/socket_helper.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/blake2/blake2.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bn_extra/bn_asn1.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bn_extra/convert.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/buf/buf.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bytestring/asn1_compat.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bytestring/ber.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bytestring/cbb.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bytestring/cbs.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/bytestring/unicode.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/chacha/chacha.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cipher_extra/cipher_extra.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cipher_extra/derive_key.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cipher_extra/e_aesctrhmac.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cipher_extra/e_aesgcmsiv.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cipher_extra/e_chacha20poly1305.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cipher_extra/e_des.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cipher_extra/e_null.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cipher_extra/e_rc2.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cipher_extra/e_rc4.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cipher_extra/e_tls.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cipher_extra/tls_cbc.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/conf/conf.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cpu_aarch64_apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cpu_aarch64_fuchsia.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cpu_aarch64_openbsd.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cpu_aarch64_linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cpu_aarch64_sysreg.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cpu_aarch64_win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cpu_arm_freebsd.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cpu_arm_linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/cpu_intel.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/crypto.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/curve25519/curve25519.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/curve25519/curve25519_64_adx.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/curve25519/spake25519.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/des/des.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/dh_extra/dh_asn1.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/dh_extra/params.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/digest_extra/digest_extra.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/dsa/dsa.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/dsa/dsa_asn1.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/ec_extra/ec_asn1.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/ec_extra/ec_derive.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/ec_extra/hash_to_curve.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/ecdh_extra/ecdh_extra.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/ecdsa_extra/ecdsa_asn1.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/engine/engine.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/err/err.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/evp.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/evp_asn1.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/evp_ctx.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/p_dsa_asn1.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/p_ec.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/p_ec_asn1.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/p_ed25519.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/p_ed25519_asn1.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/p_hkdf.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/p_rsa.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/p_rsa_asn1.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/p_x25519.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/p_x25519_asn1.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/pbkdf.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/print.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/scrypt.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/evp/sign.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/ex_data.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/fipsmodule/fips_shared_support.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/hpke/hpke.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/fipsmodule/bcm.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/hrss/hrss.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/kyber/keccak.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/lhash/lhash.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/kyber/kyber.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/mem.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/obj/obj.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/obj/obj_xref.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pem/pem_all.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pem/pem_info.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pem/pem_lib.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pem/pem_oth.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pem/pem_pk8.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pem/pem_pkey.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pem/pem_x509.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pem/pem_xaux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pkcs7/pkcs7.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pkcs7/pkcs7_x509.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pkcs8/p5_pbev2.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pkcs8/pkcs8.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pkcs8/pkcs8_x509.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/poly1305/poly1305.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/poly1305/poly1305_arm.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/poly1305/poly1305_vec.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/pool/pool.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/rand_extra/deterministic.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/rand_extra/forkunsafe.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/rand_extra/getentropy.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/rand_extra/ios.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/rand_extra/passive.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/rand_extra/rand_extra.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/rand_extra/trusty.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/rand_extra/windows.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/rc4/rc4.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/refcount.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/rsa_extra/rsa_asn1.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/rsa_extra/rsa_crypt.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/rsa_extra/rsa_print.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/siphash/siphash.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/stack/stack.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/thread.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/thread_none.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/thread_pthread.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/thread_win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/trust_token/pmbtoken.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/trust_token/trust_token.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/trust_token/voprf.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/a_sign.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/a_digest.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/a_verify.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/algorithm.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/asn1_gen.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/by_dir.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/by_file.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/i2d_pr.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/name_print.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/policy.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/rsa_pss.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/t_crl.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/t_req.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/t_x509.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/t_x509a.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_att.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_cmp.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_d2.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_def.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_ext.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_lu.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_obj.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_req.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_set.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_trs.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_txt.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_v3.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_vfy.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509_vpm.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509cset.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509name.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509rset.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x509spki.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_algor.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_all.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_attrib.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_crl.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_exten.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_info.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_name.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_pkey.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_pubkey.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_req.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_sig.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_spki.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_val.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_x509.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509/x_x509a.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_akey.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_akeya.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_alt.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_bcons.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_bitst.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_conf.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_cpols.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_crld.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_enum.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_extku.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_genn.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_ia5.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_info.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_int.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_lib.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_ncons.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_ocsp.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_pcons.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_pmaps.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_prn.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_purp.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_skey.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/x509v3/v3_utl.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/chacha/chacha-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/cipher_extra/chacha20_poly1305_armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/aesv8-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/aesv8-gcm-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/armv8-mont-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/bn-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/ghash-neon-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/ghashv8-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/p256-armv8-asm-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/p256_beeu-armv8-asm-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/sha1-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/sha256-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/sha512-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/fipsmodule/vpaes-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-aarch64/crypto/test/trampoline-armv8-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-arm/crypto/chacha/chacha-armv4-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/aesv8-armv7-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/armv4-mont-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/bsaes-armv7-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/ghash-armv4-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/ghashv8-armv7-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/sha256-armv4-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/sha1-armv4-large-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/sha512-armv4-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-arm/crypto/fipsmodule/vpaes-armv7-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-arm/crypto/test/trampoline-armv4-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86/crypto/chacha/chacha-x86-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/aesni-x86-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/bn-586-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/co-586-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/ghash-ssse3-x86-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/ghash-x86-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/md5-586-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/sha1-586-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/sha256-586-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/sha512-586-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/vpaes-x86-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86/crypto/fipsmodule/x86-mont-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86/crypto/test/trampoline-x86-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/chacha/chacha-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/cipher_extra/aes128gcmsiv-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/cipher_extra/chacha20_poly1305_x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/aesni-gcm-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/aesni-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/ghash-ssse3-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/ghash-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/md5-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/p256-x86_64-asm-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/p256_beeu-x86_64-asm-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/rdrand-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/rsaz-avx2-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/sha1-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/sha256-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/sha512-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/vpaes-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/x86_64-mont-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/fipsmodule/x86_64-mont5-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/apple-x86_64/crypto/test/trampoline-x86_64-apple.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/chacha/chacha-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/cipher_extra/chacha20_poly1305_armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/aesv8-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/aesv8-gcm-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/armv8-mont-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/bn-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/ghash-neon-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/ghashv8-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/p256-armv8-asm-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/p256_beeu-armv8-asm-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/sha1-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/sha256-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/sha512-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/fipsmodule/vpaes-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-aarch64/crypto/test/trampoline-armv8-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-arm/crypto/chacha/chacha-armv4-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/aesv8-armv7-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/armv4-mont-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/bsaes-armv7-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/ghash-armv4-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/ghashv8-armv7-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/sha1-armv4-large-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/sha256-armv4-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/sha512-armv4-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-arm/crypto/fipsmodule/vpaes-armv7-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-arm/crypto/test/trampoline-armv4-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86/crypto/chacha/chacha-x86-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/aesni-x86-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/bn-586-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/co-586-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/ghash-ssse3-x86-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/ghash-x86-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/md5-586-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/sha1-586-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/sha256-586-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/sha512-586-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/vpaes-x86-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86/crypto/fipsmodule/x86-mont-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86/crypto/test/trampoline-x86-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/chacha/chacha-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/cipher_extra/aes128gcmsiv-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/cipher_extra/chacha20_poly1305_x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/aesni-gcm-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/aesni-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/ghash-ssse3-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/ghash-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/md5-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/p256-x86_64-asm-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/p256_beeu-x86_64-asm-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/rdrand-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/rsaz-avx2-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/sha1-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/sha256-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/sha512-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/vpaes-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/x86_64-mont-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/fipsmodule/x86_64-mont5-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/linux-x86_64/crypto/test/trampoline-x86_64-linux.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/curve25519/asm/x25519-asm-arm.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/hrss/asm/poly_rq_mul.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/crypto/poly1305/poly1305_arm_asm.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/third_party/fiat/asm/fiat_curve25519_adx_mul.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/chacha/chacha-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/src/third_party/fiat/asm/fiat_curve25519_adx_square.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/cipher_extra/chacha20_poly1305_armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/aesv8-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/aesv8-gcm-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/armv8-mont-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/bn-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/ghash-neon-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/p256-armv8-asm-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/ghashv8-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/p256_beeu-armv8-asm-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/sha1-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/sha256-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/sha512-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/fipsmodule/vpaes-armv8-win.o
building out/build-imx8mp/host_libs/obj/ssl-static/opensource_libs/boringssl/win-aarch64/crypto/test/trampoline-armv8-win.o
building out/build-imx8mp/host_libs/obj/app_manifest-static/kernel/rctee/lib/app_manifest/app_manifest.o
create TA_MANIFEST_BIN: user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_1/manifest.json to out/build-imx8mp/user_product/ta/dynamic_ta_test_1/dynamic_ta_test_1.manifest
/home/<USER>/codebase/trusty/prebuilts/build-tools/linux-x86/bin/py3-cmd build/scripts/manifest_compiler.py -iuser/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_1/manifest.json -o out/build-imx8mp/user_product/ta/dynamic_ta_test_1/dynamic_ta_test_1.manifest  --header-dir out/build-imx8mp/user_product/ta/dynamic_ta_test_1/constants/include \
--enable-shadow-call-stack --default-shadow-call-stack-size 4096
user compiling c file: user/base/lib/libutee/user_header.c
user compiling c file: user/base/lib/libutee/tee_api_property.c
creating static lib: out/build-imx8mp/user_product/lib/libutee/liblibutee.a
user compiling c file: user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_1/main.c
user compiling c file: user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_1/user_ta_header.c
aring out/build-imx8mp/host_libs/libapp_manifest-static.a
generate TA_SYMS_ELF: out/build-imx8mp/user_product/ta/dynamic_ta_test_1/dynamic_ta_test_1.syms.elf
TA_LD_FIRST_OBJ=out/build-imx8mp/user_product/lib/libc-main/opensource_libs/musl/crt/rcrt1.o TA_LINK_BINS= out/build-imx8mp/user_product/lib/libc-ext/liblibc-ext.a out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a out/build-imx8mp/user_product/lib/libutee/liblibutee.a out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a out/build-imx8mp/user_product/lib/rng/librng.a out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a out/build-imx8mp/user_product/lib/tipc/libtipc.a
/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/ld.lld  -z max-page-size=4096 -z separate-loadable-segments --undefined=__aeabi_unwind_cpp_pr0 --gc-sections -static -pie --no-dynamic-linker -z text -Bsymbolic  -Lout/build-imx8mp/user_product/lib/dlmalloc  -Lout/build-imx8mp/user_product/lib/libc-ext  -Lout/build-imx8mp/user_product/lib/libc-rctee  -Lout/build-imx8mp/user_product/lib/libcxxabi-trusty  -Lout/build-imx8mp/user_product/lib/libstdc++-trusty  -Lout/build-imx8mp/user_product/lib/libutee  -Lout/build-imx8mp/user_product/lib/line-coverage  -Lout/build-imx8mp/user_product/lib/rng  -Lout/build-imx8mp/user_product/lib/syscall-stubs  -Lout/build-imx8mp/user_product/lib/tipc  -ldlmalloc  -llibc-ext  -llibc-rctee  -llibcxxabi-trusty  -llibstdc++-trusty  -llibutee  -lline-coverage  -lrng  -lsyscall-stubs  -ltipc  --start-group out/build-imx8mp/user_product/lib/libc-main/opensource_libs/musl/crt/rcrt1.o     out/build-imx8mp/user_product/ta/dynamic_ta_test_1/user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_1/main.o out/build-imx8mp/user_product/ta/dynamic_ta_test_1/user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_1/user_ta_header.o      out/build-imx8mp/user_product/lib/libc-ext/liblibc-ext.a  out/build-imx8mp/user_product/lib/dlmalloc/libdlmalloc.a  out/build-imx8mp/user_product/lib/libc-rctee/liblibc-rctee.a  out/build-imx8mp/user_product/lib/libcxxabi-trusty/liblibcxxabi-trusty.a  out/build-imx8mp/user_product/lib/libstdc++-trusty/liblibstdc++-trusty.a  out/build-imx8mp/user_product/lib/libutee/liblibutee.a  out/build-imx8mp/user_product/lib/line-coverage/libline-coverage.a  out/build-imx8mp/user_product/lib/rng/librng.a  out/build-imx8mp/user_product/lib/syscall-stubs/libsyscall-stubs.a  out/build-imx8mp/user_product/lib/tipc/libtipc.a /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/bin/../runtimes_ndk_cxx/libclang_rt.builtins-aarch64-android.a  --end-group -o out/build-imx8mp/user_product/ta/dynamic_ta_test_1/dynamic_ta_test_1.syms.elf
stripping TA_ELF: out/build-imx8mp/user_product/ta/dynamic_ta_test_1/dynamic_ta_test_1.syms.elf
page aligning TA: out/build-imx8mp/user_product/ta/dynamic_ta_test_1/dynamic_ta_test_1.syms.elf
aring out/build-imx8mp/host_libs/libssl-static.a
linking out/build-imx8mp/host_tools/apploader_package_tool
building UNPACKED_TA: out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.ta.initial from out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.elf
building UNPACKED_TA: out/build-imx8mp/user_product/ta/manifest-test/manifest-test.ta.initial from out/build-imx8mp/user_product/ta/manifest-test/manifest-test.elf
building UNPACKED_TA: out/build-imx8mp/user_product/ta/receiver/receiver.ta.initial from out/build-imx8mp/user_product/ta/receiver/receiver.elf
building UNPACKED_TA: out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.ta.initial from out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.elf
building UNPACKED_TA: out/build-imx8mp/user_product/ta/apploader/apploader.ta.initial from out/build-imx8mp/user_product/ta/apploader/apploader.elf
building UNPACKED_TA: out/build-imx8mp/user_product/ta/dynamic_ta_test_1/dynamic_ta_test_1.ta.initial from out/build-imx8mp/user_product/ta/dynamic_ta_test_1/dynamic_ta_test_1.elf
building signed TA: out/build-imx8mp/user_product/ta/manifest-test/manifest-test.signed from out/build-imx8mp/user_product/ta/manifest-test/manifest-test.ta.initial
building signed TA: out/build-imx8mp/user_product/ta/receiver/receiver.signed from out/build-imx8mp/user_product/ta/receiver/receiver.ta.initial
building signed TA: out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.signed from out/build-imx8mp/user_product/ta/hwcrypto/hwcrypto.ta.initial
building signed TA: out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.signed from out/build-imx8mp/user_product/ta/tongsuo_test/tongsuo_test.ta.initial
building signed TA: out/build-imx8mp/user_product/ta/dynamic_ta_test_1/dynamic_ta_test_1.signed from out/build-imx8mp/user_product/ta/dynamic_ta_test_1/dynamic_ta_test_1.ta.initial
building signed TA: out/build-imx8mp/user_product/ta/apploader/apploader.signed from out/build-imx8mp/user_product/ta/apploader/apploader.ta.initial
make[2]: Leaving directory '/home/<USER>/codebase/trusty-tee'
make[1]: Leaving directory '/home/<USER>/codebase/trusty-tee'
-rwxrwxr-x 1 <USER> <GROUP> 1412184 Jun 25 00:56 /home/<USER>/codebase/nxp-plus/vendor/nxp/fsl-proprietary/uboot-firmware/imx8m/tee-imx8mp.bin
